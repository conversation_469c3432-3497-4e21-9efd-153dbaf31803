package com.rewardoor.app.controller

import com.rewardoor.app.dao.SBTWhiteListRepository
import com.rewardoor.app.services.AirDropService
import com.rewardoor.app.services.UserService
import com.rewardoor.model.UserAirDropInfo
import org.springframework.beans.factory.annotation.Value
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.web.bind.annotation.*
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import org.ton.java.address.Address
import org.ton.java.cell.Cell
import org.ton.java.cell.CellBuilder
import org.ton.java.mnemonic.Ed25519
import org.ton.java.utils.Utils
import java.math.BigInteger
import java.time.Instant
import java.util.*

@RestController
@RequestMapping("/airdrop")
class AirDropController(
    val airDropService: AirDropService,
    val userService: UserService,
    val userSBTWhiteListRepo: SBTWhiteListRepository,
    @Value("\${airdrop.ton_secret_key}") private val tonSecretKey: String
) {

    @GetMapping("/initUser/{airDropId}")
    fun initWhiteListUsers(@PathVariable("airDropId") airDropId: Long): Any? {
        val loginAddress = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(loginAddress)!!
        val loginUserId = user.userId
        if (loginUserId != 248067700078L) {
            return null
        }
        // pools-SPLASH activity id = 2772
        val userSbtList = userSBTWhiteListRepo.getUserSbtListByActivityId(2772)
        val airDropBatch = mutableListOf<UserAirDropInfo>()
        for (userSbt in userSbtList) {
            val userId = userSbt.userId
            val address = userSbt.address
            val addressType = userSbt.addressType
            if (addressType == 1) {
                val userAirDropInfo = UserAirDropInfo(
                    userId = userId,
                    chain = "TON",
                    address = address.toString(),
                    airDropId = airDropId,
                    airDropTokenCnt = 250,
                    claimedType = 0
                )
                airDropBatch.add(userAirDropInfo)
            }
        }
        val insertRowCnt = airDropService.createUserAirDropInfoBatch(airDropBatch, 1000)
        println("whitelist user num : ${userSbtList.size} and init user num : $insertRowCnt")
        return "success"
    }

    @GetMapping("claim/{airDropId}")
    fun claimAirDrop(@PathVariable("airDropId") airDropId: Long): Any? {
        val address = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(address)!!
        val userId = user.userId
        val tonAddress = user.ton.tonWallet
        if (tonAddress.isNullOrEmpty()) {
            return mapOf(
                "code" to 500,
                "status" to "Incorrect Ton address",
                "message" to "Incorrect Ton address"
            )
        }
        val userAirDropInfo = airDropService.getUserAirDropInfoByAddress(tonAddress, airDropId)
        val airDrop = airDropService.getAirDrop(airDropId)
        if (airDrop == null) {
            return mapOf(
                "code" to 500,
                "status" to "Incorrect airdrop Id",
                "message" to "Incorrect airdrop Id"
            )
        }
        if (userAirDropInfo == null) {
            return mapOf(
                "code" to 404,
                "status" to "Not Yet Eligible To Claim",
                "message" to "Looks like your address isn\'t on the list.Try another address or join our community for a helping hand.",
                "link" to "https://x.com/realtbook",
                "airDropInfo" to airDrop
            )
        }
        val time = System.currentTimeMillis() / 1000
        if (userAirDropInfo.claimedType == 2) {
            return mapOf(
                "code" to 200,
                "status" to "You have claimed your token",
                "message" to "Join our community to discover more.",
                "link" to "https://x.com/realtbook",
                "airDropInfo" to airDrop,
                "userAirDropInfo" to userAirDropInfo,
                "sign" to genSign(
                    userAirDropInfo.address,
                    Utils.toNano(userAirDropInfo.airDropTokenCnt.toLong()), time
                ),
                "time" to time
            )
        }
        val nowTime = Instant.now()
        if (airDrop.endTime!! < nowTime) {
            return mapOf(
                "code" to 400,
                "status" to "This airdrop has expired",
                "message" to "Join our community to discover more.",
                "link" to "https://x.com/realtbook",
                "airDropInfo" to airDrop,
                "userAirDropInfo" to userAirDropInfo
            )
        }
        if (airDrop.startTime!! > nowTime) {
            return mapOf(
                "code" to 400,
                "status" to "This airdrop has not started yet",
                "message" to "Join our community to get notification.",
                "link" to "https://x.com/realtbook",
                "airDropInfo" to airDrop,
                "userAirDropInfo" to userAirDropInfo
            )
        }
        // todo : claim 合约功能

        // 更新claim 状态
        userAirDropInfo.claimedType = 2
        airDropService.updateUserAirDropInfo(userAirDropInfo)
        return mapOf(
            "code" to 200,
            "status" to "You have claimed your token",
            "message" to "Join our community to discover more.",
            "link" to "https://x.com/realtbook",
            "airDropInfo" to airDrop,
            "userAirDropInfo" to userAirDropInfo,
            "sign" to genSign(
                userAirDropInfo.address,
                Utils.toNano(userAirDropInfo.airDropTokenCnt.toLong()), time
            ),
            "time" to time
        )
    }

    fun genSign(address: String, amount: BigInteger, time: Long): String {
        val cell: Cell = CellBuilder.beginCell()
            .storeAddress(Address.of(address))
            .storeInt(amount, 64)
            .storeInt(time, 64)
            .endCell()

        // 使用私钥对Cell的哈希值进行签名
        val hash = cell.hash()
        val privateKey = HexFormat.of().parseHex(tonSecretKey)
        val signature: ByteArray = Ed25519.sign(privateKey, hash)
        return HexFormat.of().formatHex(signature)
    }

}