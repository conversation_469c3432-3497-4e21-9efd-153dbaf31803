package com.rewardoor.app.dao

import com.rewardoor.app.dao.tables.TBUserEvm
import com.rewardoor.app.dao.tables.TBUserReward
import com.rewardoor.app.dao.tables.TBUserTelegram
import com.rewardoor.app.dao.tables.TBUserTon
import com.rewardoor.model.UserReward
import com.rewardoor.model.UserTon
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.springframework.stereotype.Repository
import org.springframework.transaction.annotation.Transactional

@Repository
@Transactional
class UserTonRepository {
    fun bindTonWallet(userId: Long, wallet: String, pk: String): Int {
        return TBUserTon.insertIgnore {
            it[TBUserTon.userId] = userId
            it[address] = wallet
            it[publicKey] = pk
        }.insertedCount
    }

    fun findTonWallet(wallet: String): UserTon? {
        return TBUserTon.select { TBUserTon.address eq wallet }.limit(1).map(::mapUserTon).firstOrNull()
    }

    fun getTonUserIds(): List<Long> {
        return TBUserTon.select { TBUserTon.address neq "" }.map { it[TBUserTon.userId] }.distinct()
    }

    fun getTonUserWalletByUserId(userId: Long): UserTon? {
        return TBUserTon.select { TBUserTon.userId eq userId }.limit(1).map(::mapUserTon).firstOrNull()
    }

    fun updateTonUserId(uid: Long, newUserId: Long): Int {
        return TBUserTon.update({ (TBUserTon.userId eq uid) }) {
            it[userId] = newUserId
        }
    }

    fun deleteTonUserById(userId: Long): Int {
        return TBUserTon.deleteWhere { TBUserTon.userId eq userId }
    }

    fun getTonUserCnt(): Long {
        return TBUserTon.slice(TBUserTon.address.count()).selectAll()
            .single()[TBUserTon.address.count()]
    }

    fun mapUserTon(row: ResultRow): UserTon {
        return UserTon(
            userId = row[TBUserTon.userId],
            tonWallet = row[TBUserTon.address],
            publicKey = row[TBUserTon.publicKey]
        )
    }
}