package com.rewardoor.app.dao

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.rewardoor.app.dao.tables.*
import com.rewardoor.app.utils.Funcs.copyPropsFrom
import com.rewardoor.model.*
import org.jetbrains.exposed.exceptions.ExposedSQLException
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.transactions.transaction
import org.springframework.stereotype.Repository
import java.time.LocalDateTime
import java.time.ZoneOffset
import org.jetbrains.exposed.sql.statements.InsertStatement
import org.springframework.transaction.annotation.Transactional
import java.time.Instant

@Transactional
@Repository
class ParticipantRepository {

    private val mapper = ObjectMapper()
    fun createParticipant(participant: Participant) {
        try {
            TBParticipant.insert {
                it[userId] = participant.userId
                it[campaignId] = participant.campaignId
                it[wallet] = participant.wallet
                it[pointNum] = participant.pointNum
                it[isJoin] = participant.isJoin.toString()
                it[isVisit] = participant.isVisit.toString()
                it[credentials] = mapper.writeValueAsString(participant.credentials)
                it[verifiedCredentials] = mapper.writeValueAsString(participant.verifiedCredentials)
                it[participantDate] = LocalDateTime.ofInstant(participant.participantDate, ZoneOffset.UTC)
            }
        } catch (e: ExposedSQLException) {
            if (e.message?.contains("Duplicate entry") == true) {
                TBParticipant.update({ (TBParticipant.userId eq participant.userId) and (TBParticipant.campaignId eq participant.campaignId) }) {
                    it[wallet] = participant.wallet
                    it[pointNum] = participant.pointNum
                    it[isJoin] = participant.isJoin.toString()
                    it[isVisit] = participant.isVisit.toString()
                    it[credentials] = mapper.writeValueAsString(participant.credentials)
                    it[verifiedCredentials] = mapper.writeValueAsString(participant.verifiedCredentials)
                    it[participantDate] = LocalDateTime.ofInstant(participant.participantDate, ZoneOffset.UTC)
                }
            } else {
                throw e
            }
        }
    }

    fun addResult(userCredential: UserCredential) {
        TBUserCredentialNew.insert {
            it[userId] = userCredential.userId
            it[address] = userCredential.address
            it[credentialId] = userCredential.credentialId
            it[campaignId] = userCredential.campaignId
            it[status] = userCredential.status
            it[participantDate] = LocalDateTime.ofInstant(userCredential.participantDate, ZoneOffset.UTC)
            it[isTwitterLogin] = userCredential.isTwitterLogin.toString()
            it[socialId] = userCredential.socialId
            it[socialType] = userCredential.socialType
            it[labelType] = userCredential.labelType
        }
    }

    fun updateUserCredential(userCredential: UserCredential): Int {
        return TBUserCredentialNew.update({
            (TBUserCredentialNew.userId eq userCredential.userId) and
                    (TBUserCredentialNew.credentialId eq userCredential.credentialId)
        }) {
//            it[address] = userCredential.address
//            it[campaignId] = userCredential.campaignId
//            it[status] = userCredential.status
//            it[participantDate] = LocalDateTime.ofInstant(userCredential.participantDate, ZoneOffset.UTC)
//            it[isTwitterLogin] = userCredential.isTwitterLogin.toString()
            it[socialId] = userCredential.socialId
            it[socialType] = userCredential.socialType
            it[labelType] = userCredential.labelType
        }
    }

    fun updateCredentialUserId(userCredential: UserCredential, newUserId: Long, newAddress: String): Int {
        return TBUserCredentialNew.update({ TBUserCredentialNew.userId eq userCredential.userId }) {
            it[userId] = newUserId
            it[address] = newAddress
        }
    }

    fun updateCredentialTVLAmount(userId: Long, credentialId: Long, newAmount: Long): Int {
        return TBUserCredentialNew.update({ (TBUserCredentialNew.userId eq userId) and (TBUserCredentialNew.credentialId eq credentialId) }) {
            it[amount] = newAmount
        }
    }

    fun updateCredentialTVLAmount2(userId: Long, credentialId: Long, newAmount: Long): Int {
        return TBUserCredentialNew.update({ (TBUserCredentialNew.userId eq userId) and (TBUserCredentialNew.credentialId eq credentialId) }) {
            it[amount2] = newAmount
        }
    }

    fun updateCredentialTVLAmount3(userId: Long, credentialId: Long, newAmount: Long): Int {
        return TBUserCredentialNew.update({ (TBUserCredentialNew.userId eq userId) and (TBUserCredentialNew.credentialId eq credentialId) }) {
            it[amount3] = newAmount
        }
    }

    fun updateCredentialTVLAmount4(userId: Long, credentialId: Long, newAmount: Long): Int {
        return TBUserCredentialNew.update({ (TBUserCredentialNew.userId eq userId) and (TBUserCredentialNew.credentialId eq credentialId) }) {
            it[amount4] = newAmount
        }
    }

    // TODO
//    fun updateCredentialTVLAmountInArray(userId: Long, credentialId: Long, newAmount: Long): Int {
//    }


    fun updateCredentialTVLTotalAmount(userId: Long, credentialId: Long, newAmount: Long): Int {
        val amount1 =
            TBUserCredentialNew.select { (TBUserCredentialNew.userId eq userId) and (TBUserCredentialNew.credentialId eq credentialId) }
                .singleOrNull()?.get(TBUserCredentialNew.amount) ?: 0L
        val amount2 =
            TBUserCredentialNew.select { (TBUserCredentialNew.userId eq userId) and (TBUserCredentialNew.credentialId eq credentialId) }
                .singleOrNull()?.get(TBUserCredentialNew.amount2) ?: 0L

        return TBUserCredentialNew.update({ (TBUserCredentialNew.userId eq userId) and (TBUserCredentialNew.credentialId eq credentialId) }) {
            it[totalAmount] = newAmount + amount1 + amount2
        }
    }

    fun getResult(userId: Long, credentialId: Long): UserCredential? {
        return TBUserCredentialNew.select { (TBUserCredentialNew.userId eq userId) and (TBUserCredentialNew.credentialId eq credentialId) }
            .map(::newMapper).firstOrNull()
    }

    fun addUserCampaignResult(userCampaign: UserCampaign) {
        TBUserCampaignNew.insert {
            it[userId] = userCampaign.userId
            it[address] = userCampaign.address
            it[campaignId] = userCampaign.campaignId
            it[pointNum] = userCampaign.pointNum
            it[status] = userCampaign.status
        }
    }

    fun addUserGroupResult(userGroup: UserGroup) {
        TBUserGroupNew.insert {
            it[userId] = userGroup.userId
            it[address] = userGroup.address
            it[groupId] = userGroup.groupId
            it[pointNum] = userGroup.pointNum
            it[status] = userGroup.status
        }
    }

    fun addUserRewardResult(userReward: UserReward) {
        TBUserReward.insert {
            it[rewardId] = userReward.rewardId
            it[rewardType] = userReward.rewardType
            it[suiSbtObjectId] = userReward.suiSbtObjectId
            it[txHash] = userReward.txHash
            it[userId] = userReward.userId
            it[groupId] = userReward.groupId
            it[claimType] = userReward.claimType
        }
    }

    fun updateUserReward(userReward: UserReward): Int {
        return TBUserReward.update({ (TBUserReward.rewardId eq userReward.rewardId) and (TBUserReward.userId eq userReward.userId) and (TBUserReward.groupId eq userReward.groupId) }) {
            it[rewardId] = userReward.rewardId
            it[rewardType] = userReward.rewardType
            it[suiSbtObjectId] = userReward.suiSbtObjectId
            it[txHash] = userReward.txHash
            it[userId] = userReward.userId
            it[groupId] = userReward.groupId
            it[claimType] = userReward.claimType
        }
    }

    fun updateRewardUserId(userReward: UserReward, newUserId: Long): Int {
        return TBUserReward.update({ (TBUserReward.userId eq userReward.userId) }) {
            it[userId] = newUserId
        }
    }


    fun getClaimTypeByUserId(rewardId: Long, userId: Long, groupId: Long): Int? {
        return TBUserReward
            .select { (TBUserReward.userId eq userId) and (TBUserReward.rewardId eq rewardId) and (TBUserReward.groupId eq groupId) }
            .singleOrNull()?.get(TBUserReward.claimType)
    }

    fun getUserRewardByUserId(rewardId: Long, userId: Long, groupId: Long): UserReward? {
        return TBUserReward
            .select { (TBUserReward.userId eq userId) and (TBUserReward.rewardId eq rewardId) and (TBUserReward.groupId eq groupId) }
            .map(::userRewardMapper)
            .singleOrNull()
    }

    //获取对应reward id的状态为3、4 - claimable和claimed的用户奖励记录
    fun getWinnersByRewardId(rewardId: Long, groupId: Long): List<UserReward> {
        return TBUserReward.select {
            (TBUserReward.rewardId eq rewardId) and (TBUserReward.groupId eq groupId) and (TBUserReward.claimType inList listOf(
                3,
                4
            ))
        }
            .map(::userRewardMapper)
    }

    //获取对应user id所有的状态为3、4 - claimable和claimed的用户奖励记录
    fun getRewardsByUserId(userId: Long): List<UserReward> {
        return TBUserReward.select {
            (TBUserReward.userId eq userId) and (TBUserReward.claimType inList listOf(
                3,
                4
            ))
        }
            .map(::userRewardMapper)
    }

    //获取对应user id所有sui sbt的状态为3、4 - claimable和claimed的用户奖励记录
    fun getSBTRewardsByUserId(userId: Long): List<UserReward> {
        return TBUserReward.select {
            (TBUserReward.userId eq userId) and (TBUserReward.rewardType eq 4) and (TBUserReward.claimType inList listOf(
                3,
                4
            ))
        }
            .map(::userRewardMapper)
    }

    fun getClaimedUserCount(rewardId: Long, groupId: Long, claimType: Int): Int {
        return TBUserReward.slice(TBUserReward.userId.countDistinct())
            .select { (TBUserReward.rewardId eq rewardId) and (TBUserReward.groupId eq groupId) and (TBUserReward.claimType eq claimType) }
            .first()[TBUserReward.userId.countDistinct()].toInt()
    }

    fun getClaimedUserCountByRewardId(rewardId: Long): Int {
        return TBUserReward.slice(TBUserReward.userId.countDistinct())
            .select { (TBUserReward.rewardId eq rewardId) and (TBUserReward.claimType.inList(listOf(3, 4))) }
            .first()[TBUserReward.userId.countDistinct()].toInt()
    }

    fun getClaimedUserList(rewardId: Long, groupId: Long, claimType: Int): List<Long> {
        return transaction {
            TBUserReward
                .select { (TBUserReward.rewardId eq rewardId) and (TBUserReward.groupId eq groupId) and (TBUserReward.claimType eq claimType) }
                .distinct()
                .map { it[TBUserReward.userId] }
        }
    }

    fun getClaimedUserFirst5List(rewardId: Long, groupId: Long, claimType: Int): List<Long> {
        return transaction {
            TBUserReward
                .select {
                    (TBUserReward.rewardId eq rewardId) and
                            (TBUserReward.groupId eq groupId) and
                            (TBUserReward.claimType eq claimType)
                }
                .orderBy(TBUserReward.createTime to SortOrder.ASC)
                .limit(5)
                .map { it[TBUserReward.userId] }
        }
    }


    fun getUserSbtReward(rewardId: Long, userId: Long, groupId: Long): UserReward? {
        return TBUserReward.select {
            (TBUserReward.rewardId eq rewardId)
                .and(TBUserReward.userId eq userId)
                .and(TBUserReward.groupId eq groupId)
                .and(TBUserReward.rewardType eq 3)
        }.map(::userRewardMapper).firstOrNull()
    }

    fun getTop7ClaimedSuiRewards(): List<Pair<Long, Long>> {
        return TBUserReward
            .slice(TBUserReward.rewardId, TBUserReward.rewardId.count().alias("cnt")) // 选择 rewardId 和计数列
            .select { (TBUserReward.rewardType eq 4) and (TBUserReward.claimType inList listOf(3, 4)) }
            .groupBy(TBUserReward.rewardId)
            .orderBy(TBUserReward.rewardId.count().alias("cnt"), SortOrder.DESC)  // 按计数列倒序排列
            .limit(7) // 取前 7
            .map {
                val rewardId = it[TBUserReward.rewardId]
                val cnt = it[TBUserReward.rewardId.count().alias("cnt")]
                Pair(rewardId, cnt)
            }
    }


    fun getUserSuiSbtRewardByUidAndTx(userId: Long, txHash: String): UserReward? {
        return TBUserReward.select {
            (TBUserReward.userId eq userId)
                .and(TBUserReward.txHash eq txHash)
                .and(TBUserReward.rewardType eq 4)
        }.map(::userRewardMapper).firstOrNull()
    }

    fun getPointsClaimedCnt(): Long {
        return TBUserReward.slice(TBUserReward.id.count())
            .select { TBUserReward.rewardType eq 2 }
            .single()[TBUserReward.id.count()]
    }


    fun getRewardsClaimedCnt(): Long {
        return TBUserReward.slice(TBUserReward.id.count()).selectAll()
            .single()[TBUserReward.id.count()]
    }

    data class ParticipantPoint(
        val userId: Long,
        val pointId: Long,
        val claimed: Int // 表示是否已领取
    )

    fun findClaimedPointsByUserIds(userIds: List<Long>, pointIdList: List<Long>): List<ParticipantPoint> {
        val batchSize = 1000
        val result = mutableListOf<ParticipantPoint>()
        userIds.chunked(batchSize).forEach { chunk ->
            val batchResult = TBUserReward
                .select {
                    (TBUserReward.userId inList chunk) and
                            (TBUserReward.rewardType eq 2) and
                            (TBUserReward.rewardId inList pointIdList) and
                            (TBUserReward.claimType eq 4)
                }
                .map {
                    ParticipantPoint(
                        userId = it[TBUserReward.userId],
                        pointId = it[TBUserReward.rewardId],
                        claimed = 1
                    )
                }
            result.addAll(batchResult)
        }

        return result
    }


    //获取某个user_id + group_id已经claim的 reward id数组
    fun getRewardIdListByUserIdAndGroupId(
        userId: Long,
        groupId: Long,
        rewardType: Int,
        claimType: Int
    ): Map<Long, LocalDateTime> {
        return TBUserReward
            .select { (TBUserReward.userId eq userId) and (TBUserReward.groupId eq groupId) and (TBUserReward.rewardType eq rewardType) and (TBUserReward.claimType eq claimType) }
            .associateBy({ it[TBUserReward.rewardId] }, { it[TBUserReward.updateTime] })
    }

    //获取某个user_id + group_id已经claim的 sui reward id数组
    data class SuiRewardInfo(
        val suiSbtObjectId: String,
        val updateTime: LocalDateTime
    )

    fun getSuiRewardIdListByUserIdAndGroupId(
        userId: Long,
        groupId: Long,
        rewardType: Int,
        claimType: Int
    ): Map<Long, SuiRewardInfo> {
        return TBUserReward
            .select {
                (TBUserReward.userId eq userId) and
                        (TBUserReward.groupId eq groupId) and
                        (TBUserReward.rewardType eq rewardType) and
                        (TBUserReward.claimType eq claimType)
            }
            .associate {
                it[TBUserReward.rewardId] to SuiRewardInfo(
                    suiSbtObjectId = it[TBUserReward.suiSbtObjectId],
                    updateTime = it[TBUserReward.updateTime]
                )
            }
    }

    //获取某个user_id + group_id已经claim的 reward id数组大小
    fun getCountByUserIdAndGroupId(
        userId: Long,
        groupId: Long,
        rewardType: Int,
        claimType: Int
    ): Long {
        return TBUserReward
            .select { (TBUserReward.userId eq userId) and (TBUserReward.groupId eq groupId) and (TBUserReward.rewardType eq rewardType) and (TBUserReward.claimType eq claimType) }
            .count()
    }

    //获取某个user_id + group_id数组 已经claim的 reward id数组
    fun getRewardIdListByUserIdAndGroupIds(
        userId: Long,
        groupIds: List<Long>,
        rewardType: Int,
        claimType: Int
    ): List<Map<Long, LocalDateTime>> {
        return TBUserReward
            .select { (TBUserReward.userId eq userId) and (TBUserReward.groupId inList groupIds) and (TBUserReward.rewardType eq rewardType) and (TBUserReward.claimType eq claimType) }
            .map { mapOf(it[TBUserReward.rewardId] to it[TBUserReward.updateTime]) }
    }

    fun getRewardIdListByUserIdsAndGroupIds(
        userIds: List<Long>,
        groupIds: List<Long>,
        rewardType: Int,
        claimType: Int
    ): List<UserReward> {
        return TBUserReward
            .select { (TBUserReward.userId inList userIds) and (TBUserReward.groupId inList groupIds) and (TBUserReward.rewardType eq rewardType) and (TBUserReward.claimType eq claimType) }
            .map(::userRewardMapper)
    }

    //一个活动参与的人数
    fun getParticipantNum(campaignId: Long): Int {
        return TBUserCredentialNew.slice(TBUserCredentialNew.userId)
            .select { TBUserCredentialNew.campaignId eq campaignId }
            .distinct()
            .count()
    }

    //一个credential完成的人数
    fun getCredentialUserNum(credentialId: Long): Int {
        return TBUserCredentialNew.slice(TBUserCredentialNew.userId)
            .select { TBUserCredentialNew.credentialId eq credentialId }
            .distinct()
            .count()
    }

    //一个group完成的人数
    fun getGroupUserNum(groupId: Long): Int {
        return TBUserGroupNew.slice(TBUserGroupNew.userId)
            .select { TBUserGroupNew.groupId eq groupId }
            .distinct()
            .count()
    }

    //一个group完成的用户id list
    fun getUsersByGroupId(groupId: Long): List<Long> {
        return TBUserGroupNew.slice(TBUserGroupNew.userId)
            .select { TBUserGroupNew.groupId eq groupId }.withDistinct()
            .map { it[TBUserGroupNew.userId] }
    }

    //一个活动下完成credential的人数
    fun getUserCredentialNum(campaignId: Long): Long {
        return TBUserCredentialNew
            .slice(TBUserCredentialNew.userId, TBUserCredentialNew.credentialId)  // include both fields
            .select { TBUserCredentialNew.campaignId eq campaignId }
            .groupBy(TBUserCredentialNew.userId, TBUserCredentialNew.credentialId)  // group by two fields
            .count()
    }

    fun getCampaignPointNum(campaignId: Long): Long {
        return TBUserCampaignNew
            .slice(TBUserCampaignNew.pointNum)
            .select { TBUserCampaignNew.campaignId eq campaignId }.sumOf { it[TBUserCampaignNew.pointNum] }
    }

    fun getCampaignVerifiedUserPointById(userId: Long, campaignId: Long): Long? {
        return TBUserCampaignNew.select { (TBUserCampaignNew.userId eq userId) and (TBUserCampaignNew.campaignId eq campaignId) }
            .singleOrNull()?.get(TBUserCampaignNew.pointNum)
    }

    fun getGroupVerifiedUserPointById(userId: Long, groupId: Long): Long? {
        return TBUserGroupNew.select { (TBUserGroupNew.userId eq userId) and (TBUserGroupNew.groupId eq groupId) }
            .singleOrNull()?.get(TBUserGroupNew.pointNum)
    }

    fun getUserGroupResults(userIds: List<Long>, groupIds: List<Long>): List<UserGroup> {
        return TBUserGroupNew.select { (TBUserGroupNew.userId inList userIds) and (TBUserGroupNew.groupId inList groupIds) }
            .map {
                UserGroup(
                    userId = it[TBUserGroupNew.userId],
                    address = it[TBUserGroupNew.address],
                    groupId = it[TBUserGroupNew.groupId],
                    pointNum = it[TBUserGroupNew.pointNum],
                    status = it[TBUserGroupNew.status]
                )
            }
    }

    fun getUserFirstParticipationDate(userId: Long, campaignId: Long): LocalDateTime? {
        return TBUserCredentialNew
            .select { (TBUserCredentialNew.userId eq userId) and (TBUserCredentialNew.campaignId eq campaignId) }
            .orderBy(TBUserCredentialNew.participantDate)
            .limit(1)
            .singleOrNull()?.get(TBUserCredentialNew.participantDate)
    }

    //获取某个credential的giveaway个数，就是有多少用户认证成功
    fun getCredentialGiveAway(credentialId: Long): Int {
        return TBUserCredentialNew.slice(TBUserCredentialNew.userId.countDistinct())
            .select { TBUserCredentialNew.credentialId eq credentialId }
            .first()[TBUserCredentialNew.userId.countDistinct()].toInt()
    }

    //一个活动完成任意一项任务的人
    fun getAllParticipants(campaignId: Long): List<UserCredential> {
        return TBUserCredentialNew.select { TBUserCredentialNew.campaignId eq campaignId }.map(::newMapper)
    }

    fun getAllParticipantsByCampaignIds(campaignIds: List<Long>): List<UserCredential> {
        return campaignIds.chunked(1000).flatMap { batchIds ->
            TBUserCredentialNew
                .select { TBUserCredentialNew.campaignId inList batchIds }
                .map(::newMapper)
        }
    }

    //一个活动完成任意一项任务的人 - 分页
    fun getAllParticipantsPage(campaignId: Long, page: Int, pageSize: Int): List<UserCredential> {
        val offset = (page - 1) * pageSize
        return TBUserCredentialNew.select { TBUserCredentialNew.campaignId eq campaignId }
            .orderBy(TBUserCredentialNew.participantDate, SortOrder.DESC)
            .limit(pageSize, offset.toLong()).map(::newMapper)
    }

    //一个活动完成任意一项任务的人
    fun getAllParticipantIds(campaignId: Long): List<Long> {
        return TBUserCredentialNew
            .select { TBUserCredentialNew.campaignId eq campaignId }
            .map { it[TBUserCredentialNew.userId] }.distinct()
    }

    //一个活动完成任意一项任务的人
    fun getAllParticipantIdsAndAddress(campaignId: Long): List<Pair<Long, String>> {
        return TBUserCredentialNew
            .select { TBUserCredentialNew.campaignId eq campaignId }
            .map { it[TBUserCredentialNew.userId] to it[TBUserCredentialNew.address] }.distinct()
    }

    fun getAllParticipantIdCnt(campaignId: Long): Long {
        return TBUserCredentialNew
            .slice(TBUserCredentialNew.userId)
            .select { TBUserCredentialNew.campaignId eq campaignId }
            .withDistinct()
            .count()
    }

    //一个活动完成任意一项任务的人 分页
    fun getParticipantsByPageAndLimit(campaignId: Long, page: Int, limitNum: Int): List<UserCredential> {
        val offset = (page - 1) * limitNum.toLong()

        // Step 2: 获取按 participantDate 排序后的不同 userId 列表
        val uniqueUserIds = TBUserCredentialNew
            .slice(TBUserCredentialNew.userId)
            .select { TBUserCredentialNew.campaignId eq campaignId }
            .orderBy(TBUserCredentialNew.participantDate, SortOrder.DESC)
            .withDistinct()
            .limit(limitNum, offset) // 使用 limit 和 offset 实现分页
            .map { it[TBUserCredentialNew.userId] }

        // Step 3: 根据这些 userId 获取所有相关的 TBUserCredentialNew 记录
        return TBUserCredentialNew
            .select {
                (TBUserCredentialNew.campaignId eq campaignId) and
                        (TBUserCredentialNew.userId inList uniqueUserIds)
            }
            .orderBy(TBUserCredentialNew.participantDate, SortOrder.DESC)
            .map(::newMapper)
    }

    fun getAllParticipantIdNum(campaignId: Long): Int {
        return TBUserCredentialNew.slice(TBUserCredentialNew.userId.countDistinct())
            .select { TBUserCredentialNew.campaignId eq campaignId }
            .single()[TBUserCredentialNew.userId.countDistinct()].toInt()
    }

    //一个活动在C端登陆过的人
    fun getAllLoggedInUsers(campaignId: Long): List<Participant> {
        return TBParticipant.select { TBParticipant.campaignId eq campaignId }.map(::mapper)
    }

    //一个活动在C端登陆过的Id
    fun getAllLoggedInUserIds(campaignId: Long): List<Long> {
        return TBParticipant.select { TBParticipant.campaignId eq campaignId }.map { it[TBParticipant.userId] }
            .distinct()
    }

    //一个活动在C端登陆过的人 分页
    fun getAllLoggedInUsersByPageAndLimit(campaignId: Long, page: Int, limitNum: Int): List<Participant> {
        val offsetNum = (page - 1) * limitNum.toLong()
        return TBParticipant.select { TBParticipant.campaignId eq campaignId }
            .orderBy(TBParticipant.userId)
            .limit(limitNum, offsetNum)
            .distinctBy { it[TBParticipant.userId] }
            .map(::mapper)
    }

    fun getAllLoggedInUserIdNum(campaignId: Long): Int {
        return TBParticipant.slice(TBParticipant.userId.countDistinct())
            .select { TBParticipant.campaignId eq campaignId }
            .single()[TBParticipant.userId.countDistinct()].toInt()
    }

    fun getAllLoggedInUserIdNumByCampaignIds(campaignIds: List<Long>): Int {
        return TBParticipant.select { TBParticipant.campaignId inList campaignIds }
            .map { it[TBParticipant.userId] }
            .distinct()
            .count()
    }

    fun updateParticipant(participant: Participant): Int {
        return TBParticipant.update({ (TBParticipant.userId eq participant.userId) and (TBParticipant.campaignId eq participant.campaignId) }) {
            it[userId] = participant.userId
            it[campaignId] = participant.campaignId
            it[wallet] = participant.wallet
            it[pointNum] = participant.pointNum
            it[isJoin] = participant.isJoin.toString()
            it[isVisit] = participant.isVisit.toString()
            it[credentials] = mapper.writeValueAsString(participant.credentials)
            it[verifiedCredentials] = mapper.writeValueAsString(participant.verifiedCredentials)
            it[participantDate] = LocalDateTime.ofInstant(participant.participantDate, ZoneOffset.UTC)
        }
    }

    fun getByUidAndCampaignId(userId: Long, campaignId: Long): Participant? {
        return TBParticipant.select { (TBParticipant.userId eq userId) and (TBParticipant.campaignId eq campaignId) }
            .map(::mapper).firstOrNull()
    }

    fun getByCampaignId(campaignId: Long): List<Participant>? {
        return TBParticipant.select { TBParticipant.campaignId eq campaignId }
            .map(::mapper)
    }

    private fun mapper(r: ResultRow): Participant {
        val credentials = mapper.readValue<List<Credential>>(r[TBParticipant.credentials])
        val verifiedCredentials = mapper.readValue<List<Credential>>(r[TBParticipant.verifiedCredentials])
        return Participant(
            userId = r[TBParticipant.userId],
            campaignId = r[TBParticipant.campaignId],
            wallet = r[TBParticipant.wallet],
            pointNum = r[TBParticipant.pointNum],
            isJoin = r[TBParticipant.isJoin].toBoolean(),
            isVisit = r[TBParticipant.isVisit].toBoolean(),
            credentials = credentials,
            verifiedCredentials = verifiedCredentials,
            participantDate = r[TBParticipant.participantDate].toInstant(ZoneOffset.UTC)
        )
    }

    private fun newMapper(r: ResultRow): UserCredential {
        return UserCredential(
            userId = r[TBUserCredentialNew.userId],
            address = r[TBUserCredentialNew.address],
            credentialId = r[TBUserCredentialNew.credentialId],
            campaignId = r[TBUserCredentialNew.campaignId],
            status = r[TBUserCredentialNew.status],
            participantDate = r[TBUserCredentialNew.participantDate].toInstant(ZoneOffset.UTC),
            isTwitterLogin = r[TBUserCredentialNew.isTwitterLogin].toBoolean(),
            socialId = r[TBUserCredentialNew.socialId],
            socialType = r[TBUserCredentialNew.socialType],
            labelType = r[TBUserCredentialNew.labelType],
            amount = r[TBUserCredentialNew.amount],
            amount3 = r[TBUserCredentialNew.amount3],
            amount4 = r[TBUserCredentialNew.amount4]
        )
    }

    private fun userRewardMapper(r: ResultRow): UserReward {
        return UserReward(
            rewardId = r[TBUserReward.rewardId],
            rewardType = r[TBUserReward.rewardType],
            suiSbtObjectId = r[TBUserReward.suiSbtObjectId],
            txHash = r[TBUserReward.txHash],
            userId = r[TBUserReward.userId],
            groupId = r[TBUserReward.groupId],
            claimType = r[TBUserReward.claimType],
            participantDate = r[TBUserReward.createTime].toInstant(ZoneOffset.UTC)
        )
    }

    fun getUserCredentialsByCampaign(userId: Long, campaignId: Long): List<UserCredential> {
        return TBUserCredentialNew.select { (TBUserCredentialNew.userId eq userId) and (TBUserCredentialNew.campaignId eq campaignId) }
            .map(::newMapper)
    }

    fun getUserCredentialsCountByCampaign(userId: Long, campaignId: Long): Long {
        return TBUserCredentialNew.select { (TBUserCredentialNew.userId eq userId) and (TBUserCredentialNew.campaignId eq campaignId) }
            .count()
    }

    fun getUserCredentials(userId: Long): List<UserCredential> {
        return TBUserCredentialNew.select { (TBUserCredentialNew.userId eq userId) }
            .map(::newMapper)
    }

    fun getUserCampaignResult(userId: Long, campaignId: Long): UserCampaign? {
        return TBUserCampaignNew.select { (TBUserCampaignNew.userId eq userId) and (TBUserCampaignNew.campaignId eq campaignId) }
            .firstOrNull()?.let {
                UserCampaign(
                    userId = it[TBUserCampaignNew.userId],
                    address = it[TBUserCampaignNew.address],
                    campaignId = it[TBUserCampaignNew.campaignId],
                    pointNum = it[TBUserCampaignNew.pointNum],
                    status = it[TBUserCampaignNew.status]
                )
            }
    }

    fun getUserGroupResult(userId: Long, groupId: Long): UserGroup? {
        return TBUserGroupNew.select { (TBUserGroupNew.userId eq userId) and (TBUserGroupNew.groupId eq groupId) }
            .firstOrNull()?.let {
                UserGroup(
                    userId = it[TBUserGroupNew.userId],
                    address = it[TBUserGroupNew.address],
                    groupId = it[TBUserGroupNew.groupId],
                    pointNum = it[TBUserGroupNew.pointNum],
                    status = it[TBUserGroupNew.status]
                )
            }
    }

    fun getUserCredential(userId: Long, credentialId: Long): UserCredential? {
        return TBUserCredentialNew.select { (TBUserCredentialNew.userId eq userId) and (TBUserCredentialNew.credentialId eq credentialId) }
            .map(::newMapper).firstOrNull()
    }

    fun getUserCredentialBySocialIdAndCredentialId(
        socialId: String,
        credentialId: Long,
        labelType: Int
    ): UserCredential? {
        return TBUserCredentialNew.select { (TBUserCredentialNew.socialId eq socialId) and (TBUserCredentialNew.credentialId eq credentialId) and (TBUserCredentialNew.labelType eq labelType) }
            .map(::newMapper).firstOrNull()
    }

    fun getByUser(userId: Long): List<UserCredential> {
        return TBUserCredentialNew.select { (TBUserCredentialNew.userId eq userId) and (TBUserCredentialNew.status eq 1) }
            .map(::newMapper)
    }

    fun getAllUserCredentials(): List<UserCredential> {
        return TBUserCredentialNew.selectAll().map(::newMapper)
    }

    fun getUserCredentialsByCredentialId(
        credentialId: Long
    ): List<UserCredential> {
        return TBUserCredentialNew.select { (TBUserCredentialNew.credentialId eq credentialId) }
            .map(::newMapper)
            .toList()
    }
}