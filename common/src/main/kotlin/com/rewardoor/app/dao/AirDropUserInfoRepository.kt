package com.rewardoor.app.dao

import com.rewardoor.app.dao.tables.TBUserAirDropInfo
import org.springframework.stereotype.Repository
import com.rewardoor.model.UserAirDropInfo
import org.jetbrains.exposed.sql.*
import org.springframework.transaction.annotation.Transactional
import java.time.Instant
import java.time.LocalDateTime

@Repository
@Transactional
class AirDropUserInfoRepository {

    fun createUserAirDropInfo(userAirDropInfo: UserAirDropInfo) {
        TBUserAirDropInfo.insert {
            it[userId] = userAirDropInfo.userId
            it[chain] = userAirDropInfo.chain
            it[address] = userAirDropInfo.address
            it[airDropId] = userAirDropInfo.airDropId
            it[airDropTokenCnt] = userAirDropInfo.airDropTokenCnt
            it[claimedType] = userAirDropInfo.claimedType
        }
    }

    fun updateUserAirDropInfo(userAirDropInfo: UserAirDropInfo) {
        TBUserAirDropInfo.update({ (TBUserAirDropInfo.address eq userAirDropInfo.address) and (TBUserAirDropInfo.airDropId eq userAirDropInfo.airDropId) }) { // 条件：根据 address+airDropId 更新
            it[chain] = userAirDropInfo.chain
            it[address] = userAirDropInfo.address
            it[airDropTokenCnt] = userAirDropInfo.airDropTokenCnt
            it[claimedType] = userAirDropInfo.claimedType
        }
    }

    fun createUserAirDropInfoBatch(userAirDropInfoList: List<UserAirDropInfo>, batchSize: Int = 500): Int {
        var totalInsertedCount = 0

        userAirDropInfoList.chunked(batchSize).forEach { batch ->
            val insertedRows = TBUserAirDropInfo.batchInsert(batch) { userAirDropInfo ->
                this[TBUserAirDropInfo.userId] = userAirDropInfo.userId
                this[TBUserAirDropInfo.chain] = userAirDropInfo.chain
                this[TBUserAirDropInfo.address] = userAirDropInfo.address
                this[TBUserAirDropInfo.airDropId] = userAirDropInfo.airDropId
                this[TBUserAirDropInfo.airDropTokenCnt] = userAirDropInfo.airDropTokenCnt
                this[TBUserAirDropInfo.claimedType] = userAirDropInfo.claimedType
                this[TBUserAirDropInfo.createTime] = Instant.now()
                this[TBUserAirDropInfo.updateTime] = Instant.now()
            }
            totalInsertedCount += insertedRows.size
        }

        return totalInsertedCount // 返回总共插入成功的行数
    }


    fun getUserAirDropInfoByUserId(userId: Long, airDropId: Long): UserAirDropInfo? {
        return TBUserAirDropInfo
            .select { (TBUserAirDropInfo.userId eq userId) and (TBUserAirDropInfo.airDropId eq airDropId) }
            .map(::mapUserAirDropInfo)
            .firstOrNull()
    }

    fun getUserAirDropInfoByAddress(address: String, airDropId: Long): UserAirDropInfo? {
        return TBUserAirDropInfo
            .select { (TBUserAirDropInfo.address eq address) and (TBUserAirDropInfo.airDropId eq airDropId) }
            .map(::mapUserAirDropInfo)
            .firstOrNull()
    }

    fun mapUserAirDropInfo(row: ResultRow): UserAirDropInfo {
        return UserAirDropInfo(
            userId = row[TBUserAirDropInfo.userId],
            chain = row[TBUserAirDropInfo.chain],
            address = row[TBUserAirDropInfo.address],
            airDropId = row[TBUserAirDropInfo.airDropId],
            airDropTokenCnt = row[TBUserAirDropInfo.airDropTokenCnt],
            claimedType = row[TBUserAirDropInfo.claimedType]
        )
    }


}