package com.rewardoor.app.services

import com.rewardoor.IdGenerator
import com.rewardoor.app.dao.*
import com.rewardoor.model.*
import org.springframework.security.crypto.password.PasswordEncoder
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.time.Instant
import kotlin.random.Random

@Component
@Transactional
class UserService(
    val userRepo: UserRepository,
    val idGenerator: IdGenerator,
    val passwordEncoder: PasswordEncoder,
    val participantRepo: ParticipantRepository,
    val userTwitterRepo: TwitterUserRepository,
    val tgUserRepo: TelegramUserRepository,
    val dcUserRepo: DiscordUserRepository,
    val unbindInfoRepo: UnbindInfoRepository,
    val userTonRepo: UserTonRepository,
    val userSuiRepo: UserSuiRepository,
    private val userLuckyDrawRepo: UserLuckyDrawRepository
) {

    fun randomAvatar(): String {
        val name = List(4) { com.rewardoor.app.services.UserService.Companion.chars.random() }.joinToString("")
        return "https://api.dicebear.com/5.x/bottts/svg?seed=$name"
    }

    fun getUserByAddress(address: String): User? {
        return userRepo.findUserByWallet(address)
    }

    fun getUserByPrincipal(idPrincipal: String): User? {
        return getUserById(idPrincipal.toLong())
    }

    fun getUsersById(userIds: List<Long>): List<User> {
        return userRepo.findUsersById(userIds)
    }

    fun getEvmUserCnt(): Long {
        return userRepo.getEvmAddressCount()
    }

    fun getTonUserCnt(): Long {
        return userTonRepo.getTonUserCnt()
    }

    fun getSuiUserCnt(): Long {
        return userSuiRepo.getSuiUserCnt()
    }

    fun getUserById(userId: Long): User? {
        return userRepo.findUserById(userId)
    }

    fun getEvmUserById(userId: Long): UserEvm? {
        return userRepo.getEvmUserWalletById(userId)
    }

    fun getEvmUsersByUserIdList(userIds: List<Long>): List<UserEvm>? {
        return userRepo.getEvmUsersByUserIdList(userIds)
    }

    fun getUserByTwitterUserId(twitterUserId: String): User? {
        val utInfo = userTwitterRepo.getUserInfoByTwitterId(twitterUserId) ?: return null
        return getUserById(utInfo.userId)
    }

    fun registerUserByWallet(address: String): User {
        val uid = idGenerator.getNewId()
        userRepo.addUserWithWallet(
            User(
                avatar = "https://api.dicebear.com/7.x/fun-emoji/svg?backgroundColor=b6e3f4,c0aede,d1d4f9,f6d594,fcbc34,ffd5dc,ffdfbf&backgroundType=gradientLinear&eyes=closed,closed2&mouth=lilSmile,wideSmile&seed=${uid}",
                email = "", name = "", userId = 0
            ), address, uid
        )
        val participant = Participant(
            userId = uid,
            campaignId = 0L,
            wallet = address,
            nfts = emptyList(),
            points = emptyList(),
            pointNum = 0L,
            isJoin = false,
            isVisit = false,
            credentials = emptyList(),
            verifiedCredentials = emptyList(),
            participantDate = Instant.now()
        )
        participantRepo.createParticipant(participant)
        return userRepo.findUserById(uid)!!
    }

    private fun isPassportsAbleToMerge(passportA: PassportAccounts, passportB: PassportAccounts): Boolean {
        return when {
            (passportA.evmAddress != "" && passportB.evmAddress != "") || (passportA.tonAddress != "" && passportB.tonAddress != "")
                    || (passportA.suiAddress != "" && passportB.suiAddress != "")
                    || (passportA.twitterName != "" && passportB.twitterName != "") || (passportA.dcName != "" && passportB.dcName != "")
                    || (passportA.tgName != "" && passportB.tgName != "") -> false

            else -> true
        }
    }

    fun bindEvmWallet(user: User, address: String): Int {
        if (userRepo.findEvmWallet(address) != null) {
            // 说明address 已有passport，判断能不能merge

            val passportA = PassportAccounts(
                userId = user.userId,
                evmAddress = user.evm.evmWallet ?: "",
                tonAddress = user.ton.tonWallet ?: "",
                twitterName = user.twitterName,
                dcName = user.dcName,
                tgName = user.tgName
            )
            val addressUserId = getUserByAddress(address)!!.userId
            val addressUser = getUserById(addressUserId)!!
            val passportB = PassportAccounts(
                userId = addressUserId,
                evmAddress = addressUser.evm.evmWallet ?: "",
                tonAddress = addressUser.ton.tonWallet ?: "",
                twitterName = addressUser.twitterName,
                dcName = addressUser.dcName,
                tgName = addressUser.tgName
            )
            val isAbleToMerge = isPassportsAbleToMerge(passportA, passportB)
            if (!isAbleToMerge) {
                return 0
            } else {
                return -1
            }
//
//            val curUserId = userRepo.findEvmWallet(address)!!.userId
//            val curTwitterUser = userTwitterRepo.getUserInfo(curUserId)
//            if (curTwitterUser != null) { // The current address has already been linked to another Twitter account.
//                return 0
//            }
//            val mergeUserId = curUserId
//            if (utInfo != null && mergeUserId != null) {
////                updateTwitterUserId(utInfo, mergeUserId)
//                return -1
//            } else {
//                return 0
//            }
        }
        return userRepo.bindEvmWallet(user.userId, address)
    }

    fun bindTonWallet(userId: Long, address: String, pk: String): Boolean {
        val current = userTonRepo.findTonWallet(address)

        if (current != null) {
            return false
        } else {
            userTonRepo.bindTonWallet(userId, address, pk)
            val luckyDrawResult = LuckyDrawResult(
                userId = userId,
                fissionLevel = -1, //invite friend
                tPointsNum = 5000,
                isEligibleToGenerateWiseScore = 0,
                isEligibleToGenerateSBT = 0,
                createTime = Instant.now(),
                updateTime = Instant.now()
            )
            userLuckyDrawRepo.createUserLuckyDraw(luckyDrawResult)
        }
        return true
    }

    fun bindSuiWallet(userId: Long, address: String, pk: String): Boolean {
        val current = userSuiRepo.findSuiWallet(address)

        if (current != null) {
            return false
        } else {
            userSuiRepo.bindSuiWallet(userId, address, pk)
            val luckyDrawResult = LuckyDrawResult(
                userId = userId,
                fissionLevel = -1, //invite friend
                tPointsNum = 5000,
                isEligibleToGenerateWiseScore = 0,
                isEligibleToGenerateSBT = 0,
                createTime = Instant.now(),
                updateTime = Instant.now()
            )
            userLuckyDrawRepo.createUserLuckyDraw(luckyDrawResult)
        }
        return true
    }

    fun getTonWalletByUserId(userId: Long): UserTon {
        return userTonRepo.getTonUserWalletByUserId(userId) ?: UserTon(userId, null, null)
    }

    fun getTonWalletByAddress(address: String): UserTon? {
        return userTonRepo.findTonWallet(address)
    }

    fun getSuiWalletByUserId(userId: Long): UserSui {
        return userSuiRepo.getSuiUserWalletByUserId(userId) ?: UserSui(userId, null, null)
    }

    fun getSuiWalletByAddress(address: String): UserSui? {
        return userSuiRepo.findSuiWallet(address)
    }

    fun updateTwitterUserId(userTwitterInfo: UserTwitterInfo, newUserId: Long) {
        return userTwitterRepo.updateUserId(userTwitterInfo, newUserId)
    }

    fun updateTwiUserId(userId: Long, newUserId: Long): Int {
        return userTwitterRepo.updateTwitterUserId(userId, newUserId)
    }

    fun updateTonUserId(userId: Long, newUserId: Long): Int {
        val ret = userTonRepo.updateTonUserId(userId, newUserId)
        val luckyDrawResult = LuckyDrawResult(
            userId = newUserId,
            fissionLevel = -1,
            tPointsNum = 5000,
            isEligibleToGenerateWiseScore = 0,
            isEligibleToGenerateSBT = 0,
            createTime = Instant.now(),
            updateTime = Instant.now()
        )
        userLuckyDrawRepo.createUserLuckyDraw(luckyDrawResult)
        return ret
    }

    fun updateCredentialUserId(userCredential: UserCredential, newUserId: Long, newAddress: String): Int {
        return participantRepo.updateCredentialUserId(userCredential, newUserId, newAddress)
    }

    fun updateRewardUserId(userReward: UserReward, newUserId: Long): Int {
        return participantRepo.updateRewardUserId(userReward, newUserId)
    }

    fun getRewardsByUserId(userId: Long): List<UserReward> {
        return participantRepo.getRewardsByUserId(userId)
    }

    fun unbindTwitterUser(twitterId: String): Int {
        return userTwitterRepo.deleteTwitterUser(twitterId)
    }

    fun unbindTgUser(userId: Long): Int {
        return tgUserRepo.deleteTgUser(userId)
    }

    fun unbindTonUser(userId: Long): Int {
        return userTonRepo.deleteTonUserById(userId)
    }

    fun unbindSuiUser(userId: Long): Int {
        return userSuiRepo.deleteSuiUserById(userId)
    }


    fun addUnbindInfo(unbindInfo: UnbindInfo) {
        if (unbindInfoRepo.getUnbindInfo(unbindInfo.userId, unbindInfo.socialId, unbindInfo.credentialId) == null) {
            unbindInfoRepo.addUnbindInfo(unbindInfo)
        }
    }

    fun unbindDcUser(userId: Long): Int {
        return dcUserRepo.deleteDcInfo(userId)
    }

    fun updateNewUserStatus(userId: Long, newUser: Boolean) {
        return userRepo.updateNewUserStatus(userId, newUser)
    }

    fun getUserVerifiedCredentials(userId: Long): List<UserCredential> {
        return participantRepo.getUserCredentials(userId)
    }

    fun registerSuiUser(wallet: String, publicKey: String): Long {
        val current = userSuiRepo.findSuiWallet(wallet)
        if (current != null) {
            return current.userId
        }

        val newId = idGenerator.getNewId()
        userRepo.addUser(
            User(
                newId,
                "https://api.dicebear.com/7.x/fun-emoji/svg?backgroundColor=b6e3f4,c0aede,d1d4f9,f6d594,fcbc34,ffd5dc,ffdfbf&backgroundType=gradientLinear&eyes=closed,closed2&mouth=lilSmile,wideSmile&seed=${newId}",
                "",
                ""
            ), newId
        )

        userSuiRepo.bindSuiWallet(newId, wallet, publicKey)
        val participant = Participant(
            userId = newId,
            campaignId = 0L,
            wallet = "",
            nfts = emptyList(),
            points = emptyList(),
            pointNum = 0L,
            isJoin = false,
            isVisit = false,
            credentials = emptyList(),
            verifiedCredentials = emptyList(),
            participantDate = Instant.now()
        )
        participantRepo.createParticipant(participant)
        val luckyDrawResult = LuckyDrawResult(
            userId = newId,
            fissionLevel = -1,
            tPointsNum = 5000,
            isEligibleToGenerateWiseScore = 0,
            isEligibleToGenerateSBT = 0,
            createTime = Instant.now(),
            updateTime = Instant.now()
        )
        userLuckyDrawRepo.createUserLuckyDraw(luckyDrawResult)
        return newId
    }

    fun transVerifiedCredentialsAndRewards(userId: Long, newUserId: Long) {
        val newUser = getUserById(newUserId)!!
        val newAddress = newUser.wallet
        val verifiedCredentials = getUserVerifiedCredentials(userId)
        val newVerifiedCredentials = getUserVerifiedCredentials(newUserId)
        val newCredentialIds = newVerifiedCredentials.map { it.credentialId }.toSet()
        val transCredentials = verifiedCredentials.filterNot { it.credentialId in newCredentialIds }
        val userRewards = getRewardsByUserId(userId)
        val newUserRewards = getRewardsByUserId(newUserId)
        val newUserRewardIds = newUserRewards.map { it.rewardId }.toSet()
        val transRewards = userRewards.filterNot { it.rewardId in newUserRewardIds }
        for (credential in transCredentials) {
            updateCredentialUserId(credential, newUserId, newAddress)
        }
        for (reward in transRewards) {
            updateRewardUserId(reward, newUserId)
        }
    }

    companion object {
        val chars = ('a'..'z')
    }
}
