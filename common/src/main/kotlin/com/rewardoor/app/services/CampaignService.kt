package com.rewardoor.app.services

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.google.gson.Gson
import com.rewardoor.IdGenerator
import com.rewardoor.app.dao.*
import com.rewardoor.app.dto.Attendee
import com.rewardoor.app.dto.CampaignStats
import com.rewardoor.app.utils.getCredentialForm
import com.rewardoor.enums.Actions
import com.rewardoor.enums.Tags
import com.rewardoor.model.*
import org.springframework.beans.factory.annotation.Value
import org.springframework.core.env.Environment
import org.springframework.data.redis.core.ScanOptions
import org.springframework.data.redis.core.StringRedisTemplate
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.Instant
import java.time.ZoneOffset
import kotlin.random.Random

@Service
@Transactional
class CampaignService(
    val campaignRepo: CampaignRepository,
    val resultReportRepo: ResultReportRepository,
    val credentialRepo: CredentialRepository,
    val nftRepository: NFTRepository,
    val pointRepo: PointRepository,
    val projectRepo: ProjectRepository,
    val idGenerator: IdGenerator,
    val credentialGroupService: CredentialGroupService,
    val participantRepo: ParticipantRepository,
    val userRepo: UserRepository,
    val credentialGroupRepo: CredentialGroupRepository,
    val userTwitterService: UserTwitterService,
    val discordService: DiscordService,
    val telegramService: TelegramService,
    private val mailService: MailService,
    val redisTemplate: StringRedisTemplate,
    val wiseScoreService: WiseScoreService,
    val sbtRewardRepo: SBTRewardRepository,
    val sbtWhiteListRepo: SBTWhiteListRepository,
    private val userTonRepository: UserTonRepository,
    private val tonSocietySyncRepository: TonSocietySyncRepository,
    private val suiSbtRepository: SuiSbtRepository,
    private val env: Environment,
    @Value("\${ton.sync.mail_receivers}") private val mailReceivers: MutableList<String>
) {
    private val mapper = ObjectMapper()
    private val logger = mu.KotlinLogging.logger {}

    @Transactional
    fun createCampaign(campaign: Campaign): Campaign {
        val campaignId = idGenerator.getNewId()
        campaign.campaignId = campaignId
        val rewardRequest = emptyList<CampaignReward>() //mapper.readValue<List<CampaignReward>>(campaign.reward)
        campaignRepo.createCampaign(campaign)
        campaignRepo.addCampaignCredential(campaignId,
            rewardRequest.flatMap { it.credentials }.map { it.toLong() to idGenerator.getNewId() })
        return getCampaignById(campaignId)!!
    }

    @Transactional
    fun createCampaignNew(campaignTotal: CampaignTotal, userId: Long): CampaignTotal {
        val campaignId = idGenerator.getNewId()
        campaignTotal.campaign.campaignId = campaignId
//        val groups = mapper.readValue<List<CredentialGroup>>(campaignTotal.groups) =
        try {
            val groups = campaignTotal.groups
            // 新增创建campaign时需要review的逻辑
            campaignTotal.campaign.status = CampaignStatus.UNDER_REVIEWED
            // 新增邮件发送：
            val p = if (env.activeProfiles.contains("prod")) "PROD" else "STAG"
            mailService.sendTBook(
                mailReceivers,
                "Create [$p] Campaign Check",
                "New Project:\n\n ${mapper.writeValueAsString(campaignTotal.groups)}"
            )
            campaignRepo.createCampaign(campaignTotal.campaign)
            var isCampaignUnderReviewed = false
            for (group in groups) {
                // add group
                group.campaignId = campaignId
                credentialGroupService.createCredentialGroup(group, campaignId, userId)
//                if (group.sbtList.isNotEmpty()) {
//                    isCampaignUnderReviewed = true
//                }
            }
//            if (isCampaignUnderReviewed) {
//                campaignRepo.updateCampaignStatus(campaignTotal.campaign, CampaignStatus.UNDER_REVIEWED.value)
//                campaignTotal.campaign.status = CampaignStatus.UNDER_REVIEWED
//            }
            return updateCampaignStatus(campaignTotal)!!
        } catch (e: Exception) {
            logger.error("createCampaignNew error", e)
            throw e
        }
    }

    fun updateCampaignStatus(campaignTotal: CampaignTotal): CampaignTotal? {
        val nowStatus = campaignTotal.campaign.status
        if (nowStatus == CampaignStatus.DELETED || nowStatus == CampaignStatus.UNDER_REVIEWED || nowStatus == CampaignStatus.REJECTED) {
            return campaignTotal
        }
        val campaignId = campaignTotal.campaign.campaignId
        val startAt = campaignTotal.campaign.startAt
        val endAt = campaignTotal.campaign.endAt
        val currentTime = Instant.now()
        val compareStart =
            startAt?.compareTo(currentTime) ?: 0 // startAt 在当前时间之前，返回负数,startAt 等于当前时间，返回 0,startAt 在当前时间之后，返回正数
        val compareEnd = endAt?.compareTo(currentTime) ?: 0 // endAt 在当前时间之前，返回负数,endAt 等于当前时间，返回 0,endAt 在当前时间之后，返回正数
        if (compareStart > 0 && nowStatus != CampaignStatus.SCHEDULED) {
            //startAt 在当前时间之后， 活动未开始，状态为Scheduled：
            campaignTotal.campaign.status = CampaignStatus.SCHEDULED
            campaignRepo.updateCampaignStatus(campaignTotal.campaign, CampaignStatus.SCHEDULED.value)
        } else if (compareStart <= 0 && compareEnd >= 0 && nowStatus != CampaignStatus.ON_GOING) {
            // startAt 在当前时间之前，活动已开始，endAt 在当前时间之后，活动未结束
            campaignTotal.campaign.status = CampaignStatus.ON_GOING
            campaignRepo.updateCampaignStatus(campaignTotal.campaign, CampaignStatus.ON_GOING.value)
        } else if (compareEnd < 0 && nowStatus != CampaignStatus.COMPLETED) {
            // endAt 在当前时间之前，活动已结束
            campaignTotal.campaign.status = CampaignStatus.COMPLETED
            campaignRepo.updateCampaignStatus(campaignTotal.campaign, CampaignStatus.COMPLETED.value)
        }
        println("endAt is " + endAt + " currentTime is " + currentTime + " compareStart " + compareStart + "  compareEnd" + compareEnd + " status " + campaignTotal.campaign.status)
        return getCampaignTotalById(campaignId)
    }

    fun suspendCampaign(campaignId: Long): CampaignTotal? {
        val campaignTotal = getCampaignTotalById(campaignId)!!
        campaignTotal.campaign.status = CampaignStatus.SUSPENDED
        return getCampaignTotalById(campaignId)
    }

    fun deleteCampaign(campaignId: Long): CampaignTotal? {
        val campaignTotal = getCampaignTotalById(campaignId)!!
        campaignTotal.campaign.status = CampaignStatus.DELETED
        campaignRepo.updateCampaignStatus(campaignTotal.campaign, CampaignStatus.DELETED.value)!!
        return getCampaignTotalById(campaignId)
    }

    fun getActionList(): List<String> {
        return enumValues<Actions>().map { it.name }
    }

    fun getUserAssetByCompany(userId: Long, companyId: Long): UserAsset {
        val projects = projectRepo.getProjectListByCompany(companyId)

        val user = userRepo.findUserById(userId)!!
        val userNfts = mutableListOf<NFT>()
        val userCredentials = mutableListOf<Credential>()
        val userPoints = mutableListOf<Point>()

        for (project in projects) {
            val projectAsset = getUserAsset(userId, project.projectId)
            userNfts.addAll(projectAsset.nfts)
            userCredentials.addAll(projectAsset.credentials)
            userPoints.addAll(projectAsset.points)
        }
        val groupedCredentials: List<Credential> =
            userCredentials.groupBy { "${it.labelType}-${it.link}-${it.projectId}" }.flatMap { (_, groupedList) ->
                if (groupedList.size > 1) {
                    val totalGiveAway = groupedList.sumOf { it.giveAway }
                    val firstCredential = groupedList.first()
                    listOf(firstCredential.copy(giveAway = totalGiveAway))
                } else {
                    groupedList
                }
            }
        return UserAsset(
            user = user,
            nfts = userNfts,
            points = userPoints,
            credentials = groupedCredentials
        )
    }

    fun getUserAsset(userId: Long, projectId: Long): UserAsset {
        val user = userRepo.findUserById(userId)!!
        val campaigns = campaignRepo.getCampaignByProjectId(projectId)
        val userNfts = mutableListOf<NFT>()
        val userCredentials = mutableListOf<Credential>()
        val userPoints = mutableListOf<Point>()
        val userSBTs = mutableListOf<SBTReward>()
        val userSuiSbts = mutableListOf<SuiSbtReward>()
        for (campaign in campaigns) {
            val groups = credentialGroupService.getGroupsByCampaignId(campaign.campaignId)!!
            for (group in groups) {
                val rewardNftWithDate = participantRepo.getRewardIdListByUserIdAndGroupId(userId, group.id, 1, 4)
                rewardNftWithDate.forEach { (rewardId, date) ->
                    val nft = nftRepository.getNFTByPairId(rewardId, group.id)
                    if (nft != null) {
                        nft.claimedDate = date.toInstant(ZoneOffset.UTC)
                        nft.campaignId = campaign.campaignId
                        nft.campaignName = campaign.name
                        userNfts.add(nft)
                    }
                }
                val rewardPointWithDate = participantRepo.getRewardIdListByUserIdAndGroupId(userId, group.id, 2, 4)
                rewardPointWithDate.forEach { (rewardId, date) ->
                    val point = pointRepo.getPointById(rewardId)
                    if (point != null) {
                        point.claimedDate = date.toInstant(ZoneOffset.UTC)!!
                        point.campaignId = campaign.campaignId
                        point.campaignName = campaign.name
                        userPoints.add(point)
                    }
                }
                val rewardSBTWithDate = participantRepo.getRewardIdListByUserIdAndGroupId(userId, group.id, 3, 4)
                rewardSBTWithDate.forEach { (rewardId, date) ->
                    val sbt = sbtRewardRepo.getSBTById(rewardId)
                    if (sbt != null) {
                        sbt.claimedDate = date.toInstant(ZoneOffset.UTC)!!
                        sbt.campaignId = campaign.campaignId
                        sbt.campaignName = campaign.name
                        sbt.claimedType = SBTRewardClaimType.CLAIMED
                        val tonAddress = user.ton.tonWallet
                        if (!tonAddress.isNullOrEmpty()) {
                            sbt.uniqueLink = sbtWhiteListRepo.getUserSbtByUidAddressActivityId(
                                userId,
                                tonAddress,
                                sbt.activityId
                            )?.sbtLink
                        }
                        userSBTs.add(sbt)
                    }
                }
                val rewardSuiSbtWithDate = participantRepo.getSuiRewardIdListByUserIdAndGroupId(userId, group.id, 4, 4)
                rewardSuiSbtWithDate.forEach { (rewardId, info) -> // info - SuiRewardInfo
                    val sbt = suiSbtRepository.getSuiSbtRewardById(rewardId)
                    if (sbt != null) {
                        sbt.claimedDate = info.updateTime.toInstant(ZoneOffset.UTC) // 使用 info.updateTime
                        sbt.claimedType = SBTRewardClaimType.CLAIMED
                        sbt.campaignId = campaign.campaignId
                        sbt.suiSbtObjectId = info.suiSbtObjectId
                        userSuiSbts.add(sbt)
                    }
                }
            }
            val credentials = participantRepo.getUserCredentialsByCampaign(userId, campaign.campaignId)
            for (credential in credentials) {
                val credentialId = credential.credentialId
                val credential = credentialRepo.getCredentialById(credentialId) ?: continue
                credential.picUrl = credentialGroupService.blackPicUrlMapping(credential.labelType)
                userCredentials.add(credential)
            }
        }
        val groupedCredentials: List<Credential> =
            userCredentials.groupBy { "${it.labelType}-${it.link}-${it.projectId}" }.flatMap { (_, groupedList) ->
                if (groupedList.size > 1) {
                    val totalGiveAway = groupedList.sumOf { it.giveAway }
                    val firstCredential = groupedList.first()
                    listOf(firstCredential.copy(giveAway = totalGiveAway))
                } else {
                    groupedList
                }
            }
        return UserAsset(
            user = user,
            nfts = userNfts,
            points = userPoints,
            credentials = groupedCredentials,
            sbts = userSBTs,
            suiSbts = userSuiSbts
        )
    }

    fun getUserCampaigns(userId: Long, projectId: Long): UserCampaigns {
        val campaigns = campaignRepo.getCampaignByProjectId(projectId)!!
        val claimableCampaigns = mutableListOf<Campaign>()
        val probableCampaigns = mutableListOf<Campaign>()
        val completedCampaigns = mutableListOf<Campaign>()
        for (campaign in campaigns) {
//            val campaignTotal = getCampaignTotalById(campaign.campaignId)!!
            val groups = credentialGroupRepo.getCredentialGroupByCampaignId(campaign.campaignId)
//            val participantNum = getParticipationById(campaign.campaignId)?.participantNum
//            val participantIds = participantRepo.getAllParticipantIds(campaign.campaignId)!!.toSet()
//            val loggedInUserIds = participantRepo.getAllLoggedInUserIds(campaign.campaignId)!!
            val participantIdCnt = participantRepo.getAllParticipantIdCnt(campaign.campaignId)
//            val userIdList = participants.distinctBy { it.userId }.map { it.userId }
//            val onlyLoggedInUserIds =
//                loggedInUserIds.filter { it !in participantIds }
//            val totalIdList = participantIds + onlyLoggedInUserIds
//            val participantNum = totalIdList.size.toLong()
            val participantNum = participantIdCnt
            if (participantNum != null) {
                campaign.participantNum = participantNum
            }
//            updateCampaignStatus(campaignTotal)
            val credentialCnt = participantRepo.getUserCredentialsCountByCampaign(userId, campaign.campaignId)
            var campaignRewardsNum = 0L
            for (group in groups) {
                val nftCnt = nftRepository.getNFTGroupCntByGroupId(group.id)
                val pointCnt = pointRepo.getPointCntByGroupId(group.id)
                val hasNft = nftCnt > 0
                val hasPoint = pointCnt > 0
                if (hasNft) {
                    campaign.nft = 1
                }
                if (hasPoint) {
                    campaign.points = 1
                }
                val rewardNftWithDateCnt = participantRepo.getCountByUserIdAndGroupId(userId, group.id, 1, 3)
                val rewardPointWithDateCnt = participantRepo.getCountByUserIdAndGroupId(userId, group.id, 2, 3)
                val mergeRewardsNum = rewardNftWithDateCnt + rewardPointWithDateCnt
                campaignRewardsNum += mergeRewardsNum
            }
            if (campaign.status == CampaignStatus.ON_GOING) {
                if (credentialCnt > 0) {
                    probableCampaigns.add(campaign)
                }
            } else if (campaign.status == CampaignStatus.COMPLETED) {
                if (credentialCnt > 0) {
                    completedCampaigns.add(campaign)
                }
            }
            if (campaignRewardsNum > 0) {
                claimableCampaigns.add(campaign)
            }
        }
        return UserCampaigns(
            userId = userId,
            claimableCampaigns = claimableCampaigns,
            probableCampaigns = probableCampaigns,
            completedCampaigns = completedCampaigns
        )
    }

    fun getUserGroupNft(groupId: Long, userId: Long, nftId: Long): NFT {
        val rewardNftWithDate =
            participantRepo.getRewardIdListByUserIdAndGroupId(userId, groupId, 1, 4).filter { it.key == nftId }
        val group = credentialGroupRepo.getCredentialGroupById(groupId)!!
        val campaign = campaignRepo.getCampaignById(group.campaignId)!!
        val nft = nftRepository.getNFTByPairId(nftId, groupId)!!
        rewardNftWithDate.forEach { (rewardId, date) ->
            nft.claimedDate = date.toInstant(ZoneOffset.UTC)
            nft.campaignId = campaign.campaignId
            nft.campaignName = campaign.name
        }
        return nft
    }

    fun getAsset(projectId: Long): Asset {
        val campaigns = campaignRepo.getCampaignByProjectId(projectId)!!
        val projectNfts = mutableListOf<NFT>()
        val projectCredentials = mutableListOf<Credential>()
        val projectPoints = mutableListOf<UserCampaign>()
        val campaignCredentials = mutableListOf<Credential>()
        for (campaign in campaigns) {
            val campaignId = campaign.campaignId
            val campaignTotal = getCampaignTotalById(campaignId)!!
            val credentials = mergeCredentialLists(campaignTotal)
            for (credential in credentials) {
                val giveAway = participantRepo.getCredentialGiveAway(credential.credentialId).toLong()
                credential.giveAway = giveAway
            }
            val nfts = mergeNftLists(campaignTotal)
//            val points = mergePointLists(campaignTotal)
            val participants = participantRepo.getAllParticipants(campaignId)!!
            val userIdList = participants.distinctBy { it.userId }.map { it.userId }
//            val userPoints = mutableListOf<UserCampaign>()
//            for (userId in userIdList) {
//                val address = participants.filter { it.userId == userId }[0].address
//                var totalPointNum = 0L
//                for (group in campaignTotal.groups) {
////                    var pointNum = participantRepo.getGroupVerifiedUserPointById(userId, group.id)
//                    var pointNum = 0L
//                    val pointMapList = participantRepo.getRewardIdListByUserIdAndGroupId(userId, group.id, 2, 4)
//                    if (pointMapList != null && pointMapList.isNotEmpty()) {
//                        val pointId = pointMapList.keys.first()
//                        pointNum = pointRepo.getPointById(pointId)!!.number
//                    }
//                    totalPointNum += pointNum
//                }
//                val userCampaign = UserCampaign(
//                    userId = userId, address = address, campaignId = campaignId, pointNum = totalPointNum
//                )
//                userPoints.add(userCampaign)
//            }
            projectNfts.addAll(nfts)
            campaignCredentials.addAll(credentials)
//            projectPoints.addAll(userPoints)
        }
        val allPoints = pointRepo.getPointByProjectId(projectId)
        val pointsInfos = mutableListOf<PointInfo>()
        for (point in allPoints) {
            val claimedNum = participantRepo.getClaimedUserCount(point.pointId, point.groupId, 4)
            val campaignId = credentialGroupRepo.getCredentialGroupById(point.groupId)!!.campaignId
            val campaign = campaignRepo.getCampaignById(campaignId)
            if (campaign != null && campaign.status != CampaignStatus.DELETED && campaign.status != CampaignStatus.REJECTED) {
                val pointInfo = PointInfo(
                    netWorkId = 0,
                    pointId = point.pointId,
                    unlimited = point.unlimited,
                    rewardNum = point.rewardNum.toInt(),
                    number = point.number.toInt(),
                    claimStatus = 1,
                    totalClaimedNum = claimedNum.toLong(),
                    campaignId = campaignId
                )
                pointsInfos.add(pointInfo)
            } else {
                val pointInfo = PointInfo(
                    netWorkId = 0,
                    pointId = point.pointId,
                    unlimited = point.unlimited,
                    rewardNum = point.rewardNum.toInt(),
                    number = point.number.toInt(),
                    claimStatus = 1,
                    totalClaimedNum = claimedNum.toLong(),
                    campaignId = 0
                )
                pointsInfos.add(pointInfo)
            }
        }
        val groupedCredentials: List<Credential> =
            campaignCredentials.groupBy { "${it.labelType}-${it.link}-${it.projectId}" }.flatMap { (_, groupedList) ->
                if (groupedList.size > 1) {
                    val totalGiveAway = groupedList.sumOf { it.giveAway }
                    val firstCredential = groupedList.first()
                    listOf(firstCredential.copy(giveAway = totalGiveAway))
                } else {
                    groupedList
                }
            }
        projectCredentials.addAll(groupedCredentials)
//        val userIdPointNumMap: Map<Long, Long> = projectPoints.groupBy { it.userId } // 按 userId 进行分组
//            .mapValues { entry -> entry.value.sumOf { it.pointNum.toInt() }.toLong() } // 对每个分组的 pointNum 进行累加
//
//        val userIdAddressMap: Map<Long, String> = projectPoints.associateBy({ it.userId }, { it.address })

//        val userProjectPoints: List<UserProject> = userIdPointNumMap.map { (userId, pointNum) ->
//            UserProject(
//                userId = userId, address = userIdAddressMap[userId] ?: "", // 查找对应的 address，如果不存在则设置为空字符串
//                projectId = projectId, pointNum = pointNum
//            )
//        }
        val projectSBTs = tonSocietySyncRepository.getTonSyncHistoryByProjectId(projectId)
        val suiSBTs =
            suiSbtRepository.getSuiSbtSyncByProjectId(projectId).filter { it.objectId != "" && it.objectId != "0" }
        var checkSBTs =
            tonSocietySyncRepository.getTonSyncCheckListByProjectId(projectId)
        val checkSbts = mutableListOf<UserSbt>()
        if (checkSBTs != null) {
            checkSBTs = checkSBTs.filter { it.checkStatus != 1 && it.networkId == 0 }
            for (checkSbt in checkSBTs) {
                val checkSBT = UserSbt(
                    netWorkId = checkSbt.networkId ?: 0, //
                    claimStatus = 0, // under review
                    totalClaimedNum = 0,
                    tonSyncHistory = checkSbt,
                    campaignIdList = emptyList()
                )
                checkSbts.add(checkSBT)
            }
        }
        val userSbts = mutableListOf<UserSbt>()
        if (!projectSBTs.isNullOrEmpty()) {
            for (projectSbt in projectSBTs) {
                val activityId = projectSbt.activityId.toInt()
                val sbtRewards = sbtRewardRepo.getSBTByActivityId(activityId)
                val campaignIdList = mutableListOf<Long>()
                val totalClaimedNum = sbtWhiteListRepo.getClaimedUserCntByActivityId(activityId)
                val tonSyncHistory = tonSocietySyncRepository.getTonSyncHistoryByActivityId(activityId.toLong())!!
                tonSyncHistory.checkStatus = 1
                for (sbtReward in sbtRewards) {
                    val groupId = sbtReward.groupId
                    if (groupId > 0) {
                        val campaignId = credentialGroupRepo.getCredentialGroupById(groupId)?.campaignId ?: 0L
                        val campaign = campaignRepo.getCampaignById(campaignId)
                        if (campaign?.status != CampaignStatus.DELETED && campaign?.status != CampaignStatus.REJECTED) {
                            campaignIdList.add(campaignId)
                        }
                    }
                }
                val userSbt = UserSbt(
                    netWorkId = 0, // ton
                    claimStatus = 1, // claimable
                    totalClaimedNum = totalClaimedNum,
                    tonSyncHistory = tonSyncHistory,
                    campaignIdList = campaignIdList
                )
                userSbts.add(userSbt)
            }
        }

        if (suiSBTs.isNotEmpty()) {
            for (suiSbt in suiSBTs) {
                val suiSbtActivityId = suiSbt.suiSbtActivityId
                val suiSbtRewards = suiSbtRepository.getSuiSbtRewardByActivityId(suiSbtActivityId)
                val campaignIdList = mutableListOf<Long>()
                var campaignId = 0L
                var totalClaimedNum = 0
                for (suiSbtReward in suiSbtRewards) {
                    val groupId = suiSbtReward.groupId
                    if (groupId > 0) {
                        campaignId = credentialGroupRepo.getCredentialGroupById(groupId)!!.campaignId
                        val campaign = campaignRepo.getCampaignById(campaignId)!!
                        if (campaign.status != CampaignStatus.DELETED && campaign.status != CampaignStatus.REJECTED) {
                            campaignIdList.add(campaignId)
                        }
                        val claimedCnt = participantRepo.getClaimedUserCountByRewardId(suiSbtReward.suiSbtId)
                        totalClaimedNum += claimedCnt
                    }
                }
                var tonSyncCheck: TonSyncHistory? = null

                val tonSync = suiSbtRepository.getSuiSbtSyncById(suiSbtActivityId)
                val originSbtId = tonSync?.sbtId ?: 0L
                val originSbt = suiSbtRepository.getSuiSbtRewardById(originSbtId)
                tonSyncCheck = tonSocietySyncRepository.getTonSyncCheckBySBTId(originSbtId)
                if (tonSyncCheck != null && tonSyncCheck.checkStatus == 1) {
                    tonSyncCheck.groupId = originSbt?.groupId ?: 0L
                    tonSyncCheck.activityId = suiSbtActivityId
                    tonSyncCheck.sbtId = originSbtId
                }

                val userSbt = UserSbt(
                    netWorkId = 1, // sui
                    claimStatus = 1, // claimable
                    totalClaimedNum = totalClaimedNum.toLong(),
                    tonSyncHistory = tonSyncCheck,
                    campaignIdList = campaignIdList
                )
                userSbts.add(userSbt)
            }
        }

        return Asset(
            projectId = projectId,
            nfts = projectNfts,
            credentials = projectCredentials,
//            userPoints = userProjectPoints.sortedByDescending { it.pointNum }, //从大到小排序
            userSBTs = userSbts,
            points = pointsInfos,
            checkSBTs = checkSbts
        )
    }

    fun getTagList(): List<String> {
        return enumValues<Tags>().map { it.name }
    }

    fun getCampaignTotalById(campaignId: Long): CampaignTotal? {
        val campaign = campaignRepo.getCampaignById(campaignId) ?: return null
//        val groups =
//            mapper.writeValueAsString(
//                credentialGroupService.getCredentialGroupByCampaignId(campaignId)
//            )
        val groupList = mutableListOf<CredentialGroup>()
        val groups = credentialGroupService.getCredentialGroupByCampaignId(campaignId)!!.filter { it.status != 16 }
//        groupList.add(credentialGroupService.getDefaultGroup(campaign.projectId, campaign.creatorId)!!)
        groupList.addAll(groups)
        val campaignTotal = CampaignTotal(campaign, groupList)
        return campaignTotal
    }

    fun getCampaignTotalsByIds(campaignIds: List<Long>, campaigns: List<Campaign>): List<CampaignTotal>? {
        val groupList = mutableListOf<CredentialGroup>()
        val groups = credentialGroupService.getCredentialGroupsByCampaignIds(campaignIds)!!

//        groupList.add(credentialGroupService.getDefaultGroup(campaign.projectId, campaign.creatorId)!!)
        groupList.addAll(groups)
        val campaignTotals = mutableListOf<CampaignTotal>()
        for (campaign in campaigns) {
            val campaignGroups = groupList.filter { it.campaignId == campaign.campaignId }
            for (group in campaignGroups) {
                val hasNft = group.nftList.isNotEmpty()
                val hasPoint = group.pointList.isNotEmpty()
                if (hasNft && campaign.nft == 0L) {
                    campaign.nft = 1
                }
                if (hasPoint && campaign.points == 0) {
                    campaign.points = 1
                }
            }
            val campaignTotal = CampaignTotal(campaign, campaignGroups)
            campaignTotals.add(campaignTotal)
        }
        return campaignTotals
    }

    fun checkCampaignStatus(campaignId: Long, checkStatus: Int): Int {
        val campaignTotal = getCampaignTotalById(campaignId)!!
        val project = projectRepo.getProjectById(campaignTotal.campaign.projectId)!!
        val projectEmail = project.projectEmail
        if (checkStatus == 1) {
            campaignTotal.campaign.status = CampaignStatus.DRAFT
            updateCampaignStatus(campaignTotal)
            if (projectEmail.isNotEmpty()) {
                val newMailReceivers = mailReceivers + projectEmail
                val buttonText = "Share Now"
                val htmlContent = """
                    <!DOCTYPE html>
                    <html>
                      <body style="margin:0; padding:0; background:#f6f6f6;">
                        <table width="100%" cellpadding="0" cellspacing="0" style="background:#f6f6f6; min-height:700px;">
                          <tr>
                            <td align="center">
                              <table width="100%" cellpadding="0" cellspacing="0" style="max-width:480px; background:#fff; border-radius:16px; margin:40px auto; box-shadow:0 2px 8px rgba(0,0,0,0.04);">
                                <tr>
                                  <td align="left" style="padding:32px 0 16px 32px;">
                                    <!-- Logo -->
                                    <img src="https://static.tbook.vip/img/0f3f8cc01b974de2b98addb69cb4301a" alt="TBook Logo" style="width: 150px;">
                                    <div style="font-size:24px; font-weight:700; color:#222; font-family:sans-serif; margin-bottom:8px;;">
                                      Share Asset DeepLink to your community now!
                                    </div>
                                  </td>
                                </tr>
                                <tr>
                                  <td style="padding:0 32px 0 32px;">
                                    <p style="margin:0 0 8px 0; color:#222; font-size:16px; font-family:sans-serif;">
                                      Hey <b>${project.projectName}</b> Team,
                                    </p>
                                    <p </p>
                                    <p style="margin:0 0 24px 0; color:#222; font-size:16px; font-family:sans-serif;">
                                      Your Asset DeepLink https://engage.tbook.com/${project.projectName}/${campaignId} is ready to show with community!
                                    </p>
                                  </td>
                                </tr>
                                <tr>
                                  <td align="left" style="padding:0 32px 24px 32px;">
                                    <a
                                      href="https://engage.tbook.com/${project.projectName}/${campaignId}"
                                      style="
                                        display:inline-block;
                                        padding:15px 20px;
                                        font-size:16px;
                                        color:#fff;
                                        background-color:#904cf7;
                                        border:none;
                                        border-radius:10px;
                                        text-decoration:none;
                                        font-weight:600;
                                        font-family:sans-serif;
                                        transition:background 0.2s;
                                      "
                                      onmouseover="this.style.backgroundColor='#6d2ed6'"
                                      onmouseout="this.style.backgroundColor='#904cf7'"
                                    >$buttonText</a>
                                  </td>
                                </tr>
                                <tr>
                                  <td style="padding:0 32px 24px 32px;">
                                    <p style="color:#888; font-size:13px; font-family:sans-serif; margin:0 0 8px 0;">
                                      Have questions? We're here to help at
                                      <a href="mailto:<EMAIL>" style="color:#904cf7; text-decoration:underline;"><EMAIL></a>.
                                    </p>
                                    <!-- <p style="color:#222; font-size:16px; font-family:sans-serif; margin:0 0 4px 0;">Congratulations again,</p>
                                    <p style="color:#222; font-size:16px; font-family:sans-serif; margin:0;">TBook Team</p> -->
                                  </td>
                                </tr>
                              </table>
                            </td>
                          </tr>
                        </table>
                      </body>
                    </html>
                """.trimIndent()
                mailService.sendMessageWithButton(
                    newMailReceivers,
                    "Share Asset DeepLink to your community now!",
                    htmlContent
                )
            }
            return 1
        } else if (checkStatus == 2) {
            campaignRepo.updateCampaignStatus(campaignTotal.campaign, CampaignStatus.REJECTED.value)
            if (projectEmail.isNotEmpty()) {
                val newMailReceivers = mailReceivers + projectEmail
                val htmlContent = """
                    <!DOCTYPE html>
                    <html>
                      <body style="margin:0; padding:0; background:#f6f6f6;">
                        <table width="100%" cellpadding="0" cellspacing="0" style="background:#f6f6f6; min-height:700px;">
                          <tr>
                            <td align="center">
                              <table width="100%" cellpadding="0" cellspacing="0" style="max-width:480px; background:#fff; border-radius:16px; margin:40px auto; box-shadow:0 2px 8px rgba(0,0,0,0.04);">
                                <tr>
                                  <td align="left" style="padding:32px 0 16px 32px;">
                                    <!-- Logo -->
                                    <img src="https://static.tbook.vip/img/0f3f8cc01b974de2b98addb69cb4301a" alt="TBook Logo" style="width: 150px;">
                                    <div style="font-size:24px; font-weight:700; color:#222; font-family:sans-serif; margin-bottom:8px;;">
                                      Your Asset DeepLink needs adjustments
                                    </div>
                                  </td>
                                </tr>
                                <tr>
                                  <td style="padding:0 32px 0 32px;">
                                    <p style="margin:0 0 8px 0; color:#222; font-size:16px; font-family:sans-serif;">
                                      Hey <b>${project.projectName}</b> Team,
                                    </p>
                                    <p> </p>
                                    <p style="margin:0 0 24px 0; color:#222; font-size:16px; font-family:sans-serif;">
                                      We’re sorry, but your credentials for the asset didn't meet our current criteria for approval.
                                    </p>
                                    <p style="margin:0 0 24px 0; color:#222; font-size:16px; font-family:sans-serif;">
                                      If you have any questions, please reach out to us via:
                                    </p>
                                    <p style="margin:0 0 24px 0; color:#222; font-size:16px; font-family:sans-serif;">
                                      https://t.me/Keyla_SUE
                                    </p>
                                  </td>
                                </tr>
                                <tr>
                                  <td style="padding:0 32px 24px 32px;">
                                    <p style="color:#888; font-size:13px; font-family:sans-serif; margin:0 0 8px 0;">
                                      Have questions? We're here to help at
                                      <a href="mailto:<EMAIL>" style="color:#904cf7; text-decoration:underline;"><EMAIL></a>.
                                    </p>
                                    <!-- <p style="color:#222; font-size:16px; font-family:sans-serif; margin:0 0 4px 0;">Congratulations again,</p>
                                    <p style="color:#222; font-size:16px; font-family:sans-serif; margin:0;">TBook Team</p> -->
                                  </td>
                                </tr>
                              </table>
                            </td>
                          </tr>
                        </table>
                      </body>
                    </html>
                """.trimIndent()
                mailService.sendMessageWithButton(
                    newMailReceivers,
                    "Your Asset DeepLink needs adjustments",
                    htmlContent
                )
            }
            return 1
        }
        return 0
    }

    fun getCampaignTotalsByProjectId(projectId: Long): List<CampaignTotal>? {
        val campaigns = campaignRepo.getCampaignByProjectId(projectId)!!
            .filter { it.status != CampaignStatus.DELETED && it.status != CampaignStatus.REJECTED }
        val campaignIds = campaigns.map { it.campaignId }
//        val campaignTotals = mutableListOf<CampaignTotal>()
        val campaignTotals = getCampaignTotalsByIds(campaignIds, campaigns)!!
//        for (campaignTotal in campaignTotals) {
//            updateCampaignStatus(campaignTotal)!!
//        }
        return campaignTotals
    }

    fun getGroupsByProjectId(projectId: Long, creatorId: Long): List<CredentialGroup>? {
//        val campaignTotals = getCampaignTotalsByProjectId(projectId, creatorId)!!
//        val groups = mergeCredentialGroups(campaignTotals)
        val groups = credentialGroupService.getDefaultGroup(projectId, creatorId)!!
        return groups
    }

    fun updateCampaignNew(campaignTotal: CampaignTotal, userId: Long): CampaignTotal {
//        val groups = mapper.readValue<List<CredentialGroup>>(campaignTotal.groups)
        val groups = campaignTotal.groups
        val campaignId = campaignTotal.campaign.campaignId
        val oldCampaignTotal = getCampaignTotalById(campaignId)!!
        val oldGroups = oldCampaignTotal.groups
        val deleteGroups = oldGroups.filter { oldGroup ->
            !groups.any { newGroup ->
                newGroup.id == oldGroup.id
            }
        }
        deleteGroups.forEach { group ->
            group.status = 16
            credentialGroupService.updateCredentialGroup(group)
        }
        campaignRepo.updateCampaign(campaign = campaignTotal.campaign)
        updateCampaignStatus(campaignTotal)
        var newGroups = mutableListOf<CredentialGroup>()
        for (group in groups) {
            group.credentialList.forEach { credential ->
                println("credential label type: " + credential.credentialId + "  " + credential.labelType)
            }
            group.campaignId = campaignId
            group.creatorId = campaignTotal.campaign.creatorId
            if (group.id == 0L) { // add a new group
                newGroups.add(credentialGroupService.createCredentialGroup(group, campaignId, userId))
            }
            newGroups.add(
                credentialGroupService.updateCredentialGroup(group)!!
            )
        }
        campaignTotal.campaign = campaignRepo.getCampaignById(campaignId)!!
        campaignTotal.groups = newGroups
        return CampaignTotal(
            campaignTotal.campaign, campaignTotal.groups
        )
    }

    fun getCampaignById(campaignId: Long): Campaign? {
        val campaign = campaignRepo.getCampaignById(campaignId) ?: return null
        val credentials = credentialRepo.getCampaignCredential(campaignId)
        campaign.credentials = credentials
        return campaign
    }

    fun getParticipantById(campaignId: Long): List<Participant>? {
        return participantRepo.getByCampaignId(campaignId)
    }

    fun getParticipantByUserIdAndCampaignId(userId: Long, campaignId: Long): Participant? {
        return participantRepo.getByUidAndCampaignId(userId, campaignId)
    }

    fun addParticipant(participant: Participant): Participant? {
        participantRepo.createParticipant(participant)
        return getParticipantByUserIdAndCampaignId(participant.userId, participant.campaignId)
    }

//    fun updateParticipantBySpace(userId: Long, campaignId: Long, spaceId: String) {
//        val userTwitterInfo = userTwitterService.getUserInfo(userId)!!
//        val credential = TwitterCredentialsBearer(bearerToken)
//        val isVerified = userTwitterService.checkSpaceActivity(userTwitterInfo, credential, spaceId)
//        //campaign对应的所有credential
//        val credentialList = mergeCredentialLists(getCampaignTotalById(campaignId)!!)
//        for (credential in credentialList) {
//
//        }
//    }

    fun getCredentialForm(labelType: Int): String {

        return ""
    }

    fun getVerifiedCamByCredential(
        campaignId: Long, userId: Long, address: String
    ): CampaignTotal? {
        if (userId == 0L) { //未登录
            val campaignTotal = getCampaignTotalById(campaignId)!!
            val resultCampaignTotal = updateCampaignStatus(campaignTotal)!!
            for (group in resultCampaignTotal.groups) {
                group.credentialList.forEach() {
                    it.isVerified = 0
                }
                for (suiSbt in group.suiSbtList) {
                    val suiSbtId = suiSbt.suiSbtId
                    val holderCnt = participantRepo.getClaimedUserCountByRewardId(suiSbtId)
                    val first5HolderUidList = participantRepo.getClaimedUserFirst5List(suiSbtId, group.id, 4)
                    val userPicList = userRepo.getUsersByUserIdList(first5HolderUidList)?.map { it.avatar }
                    suiSbt.holderCnt = holderCnt
                    suiSbt.holderPicList = userPicList
                }
            }
            return resultCampaignTotal
        }
        val camTotal = getCampaignTotalById(campaignId)!!
        val gson = Gson()
        val credentials = mergeCredentialLists(camTotal)
        for (credential in credentials) {
            val json = credential.getCredentialForm()
//            val credentialForm = gson.fromJson(json, FormList::class.java)
//            credential.list = credentialForm.list
            if (credential.labelType == 12) { //snapshot
                credential.proposalId = credential.snapShotProposalId()
            }
        }
        val totalPointNum = mergePointLists(camTotal).sumOf { it.number } //活动一个用户能拿到的总Point数
        val isCampaignVerified = credentials.size == participantRepo.getUserCredentialsByCampaign(
            userId, campaignId
        ).size //验证一个活动里 一个用户是否完成所有任务
        if (isCampaignVerified) {
            updateCampaign(campaignId, userId, address, totalPointNum)
        }
        val campaignTotal = getCampaignTotalById(campaignId)!!
        val result = updateCampaignStatus(campaignTotal)!!
        println("result group credentials " + result.groups.flatMap { it.credentialList }.map { it.credentialId }
            .toString())
        val finished = participantRepo.getUserCredentialsByCampaign(userId, campaignId).associateBy { it.credentialId }
        for (credential in result.groups.flatMap { it.credentialList }.orEmpty()) {
            println("credentialId is " + credential.credentialId + "  finished Id is " + finished.keys.toString() + "user Id is " + userId + " campaignId is " + campaignId)
            if (!isUserSocialAccountAbleToVerify(
                    userId,
                    credential
                )
            ) { // this social account has been verified already, can not be verified again
                credential.isVerified = -1
            } else if (finished.containsKey(credential.credentialId)) {
                credential.isVerified = 1
//                credentialRepo.updateCredential(credential)
            }
        }
        for (group in result.groups) {
            val points = group.pointList
            for (point in points) {
                val claimType =
                    participantRepo.getClaimTypeByUserId(point.pointId, userId, group.id) //该用户已经claim或者miss过reward
                if (claimType != null) {
                    point.claimedType = claimType
                }
            }
            group.pointList = points
            val sbts = group.sbtList
            group.sbtList = sbts
            val groupTotalPointNum = points.sumOf { it.number }  //一个活动group里 用户能拿到的总Point数
//            val isGroupVerified = credentials.all { it.isVerified == 1 } //一个活动group里 用户是否完成所有任务

            val isGroupVerified = getIsGroupVerified(
                group,
                result.groups.flatMap { it.credentialList }
            )

            println(" isGroupVerified " + isGroupVerified + " group id is " + group.id + " userId is " + userId)

            for (sbt in sbts) {
                val claimType =
                    participantRepo.getClaimTypeByUserId(sbt.sbtId, userId, group.id) //该用户已经claim或者miss过reward
                if (claimType != null && isGroupVerified) {
                    sbt.claimedType = claimType
                }
            }
            for (suiSbt in group.suiSbtList) {
                val suiUserReward =
                    participantRepo.getUserRewardByUserId(suiSbt.suiSbtId, userId, group.id) //该用户已经claim或者miss过reward
                if (suiUserReward?.claimType != null && isGroupVerified) {
                    suiSbt.claimedType = suiUserReward.claimType
                    suiSbt.suiSbtObjectId = suiUserReward.suiSbtObjectId
                }
                val suiSbtId = suiSbt.suiSbtId
                val holderCnt = participantRepo.getClaimedUserCountByRewardId(suiSbtId)
                val first5HolderUidList = participantRepo.getClaimedUserFirst5List(suiSbtId, group.id, 4)
                val userPicList = userRepo.getUsersByUserIdList(first5HolderUidList)?.map { it.avatar }
                suiSbt.holderCnt = holderCnt
                suiSbt.holderPicList = userPicList
            }

//            val isPointNotClaimed = if (points.isEmpty()) false else participantRepo.getClaimTypeByUserId(
//                points[0].pointId, userId, group.id
//            ) == null
//            val pointClaimType = participantRepo.getClaimTypeByUserId(
//                points[0].pointId, userId, group.id
//            )
            val isPointNotClaimed = if (points.isEmpty()) false else {
                val claimType = participantRepo.getClaimTypeByUserId(points[0].pointId, userId, group.id)
                claimType == null || claimType !in intArrayOf(4, 5)
            }
            println(" isPointNotClaimed" + isPointNotClaimed)
            val isSBTNotClaimed = if (sbts.isEmpty()) false else {
                val claimType = participantRepo.getClaimTypeByUserId(sbts[0].sbtId, userId, group.id)
                claimType == null || claimType !in intArrayOf(4)
            }
            val isSuiSbtNotClaimed = if (group.suiSbtList.isEmpty()) false else {
                val claimType = participantRepo.getClaimTypeByUserId(group.suiSbtList[0].suiSbtId, userId, group.id)
                claimType == null || claimType !in intArrayOf(4)
            }
            val nfts = group.nftList
            val userResult = participantRepo.getUserGroupResult(userId, group.id)
            for (nft in nfts) {
                val claimType =
                    participantRepo.getClaimTypeByUserId(nft.nftId, userId, group.id) //该用户已经claim或者miss过reward
                if (claimType != null) {
                    nft.claimedType = claimType
                }
                val isNftNotClaimed = if (group.nftList.isEmpty()) false else participantRepo.getClaimTypeByUserId(
                    nft.nftId, userId, group.id
                ) == null
                println(" isNftNotClaimed " + isNftNotClaimed)
                if (isGroupVerified && isNftNotClaimed) {
                    if (userResult != null) {
                        updateUserGroup(group.id, userId, address, groupTotalPointNum)
                    }
                    updateNftClaimedType(group, userId, result.campaign)
                } else {
                    if (result.campaign.status == CampaignStatus.COMPLETED) { //campaign is over
                        nft.claimedType = 5
                    }
                }
            }
            if (isGroupVerified && isPointNotClaimed) {
//                if (userResult != null) {
                updateUserGroup(group.id, userId, address, groupTotalPointNum)
//                }
                println("point id is " + group.pointList[0].pointId + " user id " + userId + " group id " + group.id + " isGroupVerified " + isGroupVerified + " isPointNotClaimed " + isPointNotClaimed)
                updatePointClaimedType(group, userId, result.campaign)
                if (isSBTNotClaimed) {
                    updateSBTClaimedType(group, userId, result.campaign)
                }
            } else {
                if (isGroupVerified && isSBTNotClaimed) {
                    updateSBTClaimedType(group, userId, result.campaign)
                }
                if (isGroupVerified && isSuiSbtNotClaimed) {
                    updateSuiSbtClaimedType(group, userId, result.campaign)
                }
                val pointId = if (points.isNotEmpty()) points[0].pointId else 0
                val claimType = participantRepo.getClaimTypeByUserId(pointId, userId, group.id)
                if (result.campaign.status == CampaignStatus.COMPLETED && (claimType == null || claimType !in intArrayOf(
                        4,
                        5
                    ))
                ) { //campaign is over
                    group.pointList.forEach { it.claimedType = 5 }
                }
            }
        }
        return result
    }

    fun isUserSocialAccountAbleToVerify(userId: Long, credential: Credential): Boolean {

        fun verify(socialId: String?): Boolean {
            if (socialId != null) {
                val userCredential = participantRepo.getUserCredentialBySocialIdAndCredentialId(
                    socialId,
                    credential.credentialId,
                    credential.labelType
                )
                return !(userCredential != null && userCredential.userId != userId) // this social account has been verified
            }
            return true
        }

        return when {
            Credential.isTwitterTask(credential.labelType) -> {
                val twitterId = userTwitterService.getUserInfo(userId)?.twitterId
                verify(twitterId)
            }

            Credential.isDiscordTask(credential.labelType) -> {
                val dcId = discordService.getUserDcInfo(userId)?.dcId
                verify(dcId)
            }

            Credential.isTelegramTask(credential.labelType) -> {
                val tgId = telegramService.getUserInfo(userId)?.tgId
                verify(tgId?.toString())
            }

            else -> true
        }
    }

    fun getIsGroupVerified(group: CredentialGroup, verifiedCredentials: List<Credential>): Boolean {
        val credentials = group.credentialList
//        val credentialIdList: List<Long> = credentials.map { credential -> credential.credentialId }
        val verifiedCredentialList = verifiedCredentials.filter { it.isVerified == 1 && it.groupId == group.id }
        val isGroupVerified = verifiedCredentialList.size == credentials.size
        return isGroupVerified
    }

    fun getUserCredentialsByCampaign(userId: Long, campaignId: Long): List<UserCredential> {
        return participantRepo.getUserCredentialsByCampaign(userId, campaignId)
    }

    //已完成group任务有抽奖资格的用户，更新奖励point的claim type
    fun updatePointClaimedType(
        group: CredentialGroup, userId: Long, campaign: Campaign
    ): List<Point> {
        val verifiedUserNum = getUserCountByGroupId(group.id) //单个group已经认证的用户数量
        val points = group.pointList
        val limitedUserNum = points.sumOf { it.rewardNum }  //设定可以给出Point的地址总数
        var claimedNum = points.sumOf { it.claimedNum }
        val pointId = if (points.isNotEmpty()) points[0].pointId else 0
        if (claimedNum == 0L) {
            val claimedUserNum = getClaimedUserCountOfClaimType(pointId, group.id, 4).toLong() // 单个group已经认证且claim的用户数量
            if (claimedNum < claimedUserNum) {
                claimedNum = claimedUserNum
            }
        }
        println(" verifiedUserNum is " + verifiedUserNum + " limitedUserNum is " + limitedUserNum + " claimedUserNum is " + claimedNum)
        val methodType = if (points.isNotEmpty()) points[0].methodType else 0
        var userClaimedType = 0
        if (points.isNotEmpty()) {
            if (participantRepo.getClaimTypeByUserId(points[0].pointId, userId, group.id) != null) {
                userClaimedType = participantRepo.getClaimTypeByUserId(points[0].pointId, userId, group.id)!!
                println(" userClaimedType is " + userClaimedType)
            }
        }
        val isClaimable = if (points.isNotEmpty()) listOf(1, 3).contains(userClaimedType) else false
        println("userId is " + userId + "group Id is" + group.id + " pointId is " + pointId + "isClaimable " + isClaimable)
        var isPointAvailable = false
        val isUnlimited = if (points.isNotEmpty()) points[0].unlimited else false
        if (isUnlimited) {
            isPointAvailable = true
        } else {
            isPointAvailable = claimedNum < limitedUserNum
        }
        if (isClaimable) {
            if (methodType == 1) { //FCFS
                if (isPointAvailable) { //point还没发完，可以继续领
                    points.forEach {
                        it.claimedType = 4   //claimed
                        addUserReward(2, it.pointId, userId, group.id, 4)
                        val newClaimedNum = claimedNum + 1
                        it.claimedNum = newClaimedNum
                        pointRepo.updatePointClaimedNum(it.pointId, newClaimedNum)
                    }
                } else {
                    points.forEach {
                        it.claimedType = 5  //missed未获取
                        addUserReward(2, it.pointId, userId, group.id, 5)
                    }
                }
            } else if (methodType == 2 && points.isNotEmpty()) { //lucky draw
                if (isPointAvailable) {
                    if (campaign.status == CampaignStatus.COMPLETED && points[0].claimedType == 1) { //活动已结束，且point为等待抽奖状态，claimedType== 1(未抽过奖)，开启抽奖
                        val verifiedUsers = getUserIdByGroupId(group.id) //单个group已经认证且claim的用户 id列表
                        val luckyList = luckyDrawPoint(group, verifiedUserNum, limitedUserNum.toInt(), verifiedUsers)
                        if (userId in luckyList) { //中奖
                            points.forEach {
                                it.claimedType = 4   //claimed
                                addUserReward(2, it.pointId, userId, group.id, 4)
                                val newClaimedNum = claimedNum + 1
                                it.claimedNum = newClaimedNum
                                pointRepo.updatePointClaimedNum(it.pointId, newClaimedNum)
                            }
                        } else { //未中奖
                            points.forEach {
                                it.claimedType = 5   //missed未获取
                                addUserReward(2, it.pointId, userId, group.id, 5)
                            }
                        }
                    } else if (campaign.status == CampaignStatus.COMPLETED && (points[0].claimedType == 4 || points[0].claimedType == 5)) {
                        //已经抽过奖
                    } else {
                        points.forEach {
                            it.claimedType = 1   //已完成等待抽奖
                            addUserReward(2, it.pointId, userId, group.id, 1)
                        }
                    }
                } else {
                    points.forEach {
                        it.claimedType = 5  //missed未获取
                        addUserReward(2, it.pointId, userId, group.id, 5)
                    }
                }
            }
        } else if (isPointAvailable) {
            if (methodType == 2 && campaign.status != CampaignStatus.COMPLETED) { //lucky draw
                points.forEach {
                    it.claimedType = 1   // waiting for luckyDraw
                    addUserReward(2, it.pointId, userId, group.id, 1)
                }
            } else {
                points.forEach {
                    it.claimedType = 3   // claimable
                    addUserReward(2, it.pointId, userId, group.id, 3)
                }
            }
        } else {
            points.forEach {
                it.claimedType = 5   // missed
                addUserReward(2, it.pointId, userId, group.id, 5)
            }
        }
        group.pointList = points
        return points
    }

    //    Users who have completed the group tasks and are eligible for the lottery will have their reward SBT claim type updated.
//     By default, the SBTs are unlimited claimed.
    fun updateSBTClaimedType(
        group: CredentialGroup, userId: Long, campaign: Campaign
    ): List<SBTReward> {
        val sbts = group.sbtList
        val userTonAddress = userRepo.findUserById(userId)?.ton?.tonWallet
        if (!userTonAddress.isNullOrEmpty()) {
            for (sbt in sbts) {
                val userRewardClaimType = participantRepo.getClaimTypeByUserId(sbt.sbtId, userId, group.id)
                val sbtList = sbtWhiteListRepo.getUserSbtByUidAddressActivityId(userId, userTonAddress, sbt.activityId)
                if (userRewardClaimType != null && userRewardClaimType != SBTRewardClaimType.CLAIMED && sbtList != null) {
                    val claimType = wiseScoreService.getSBTClaimedStatus(sbt.activityId, userTonAddress)
                    addUserReward(3, sbt.sbtId, userId, group.id, if (claimType != 0) claimType else 1)
                    if (sbtWhiteListRepo.getUserSbtByUidAddressActivityId(
                            userId,
                            userTonAddress,
                            sbt.activityId
                        ) != null && claimType > 0
                    ) {
                        sbtWhiteListRepo.updateUserSBTClaimedType(userId, userTonAddress, sbt.activityId, claimType)
                    }
                } else {
                    addUserReward(3, sbt.sbtId, userId, group.id, 1)
                }
            }
        } else {
            sbts.forEach {
                it.claimedType = 1   //able to claim
                addUserReward(3, it.sbtId, userId, group.id, 1)
            }
        }
        return sbts
    }

    fun updateSuiSbtClaimedType(
        group: CredentialGroup, userId: Long, campaign: Campaign
    ): List<SuiSbtReward> {
        val suiSbts = group.suiSbtList
        val userSuiAddress = userRepo.findUserById(userId)?.suiAddress
        if (!userSuiAddress.isNullOrEmpty()) {
            for (sbt in suiSbts) {
                val userRewardClaimType = participantRepo.getClaimTypeByUserId(sbt.suiSbtId, userId, group.id)
                if (userRewardClaimType != null && userRewardClaimType != SBTRewardClaimType.CLAIMED) {
                    // do nothing
                } else {
                    addUserReward(4, sbt.suiSbtId, userId, group.id, 1)
                }
            }
        } else {
            suiSbts.forEach {
                it.claimedType = 1   //able to claim
                addUserReward(4, it.suiSbtId, userId, group.id, 1)
            }
        }
        return suiSbts
    }

    // init sbt user claim type
    fun initSbtClaimedType() {
        //late night defi
        val groupIdList = listOf(
            56120827779160,
            56120827779164,
            56120827779168,
            56120827779173,
            56120827779177,
            56120827779181,
            58697250869261,
            58536485864889
        )
        val userSBTList = sbtWhiteListRepo.getInitUserSbtList()
        for (userSBT in userSBTList) {
            val userId = userSBT.userId
            val userTonAddress = userTonRepository.getTonUserWalletByUserId(userId)?.tonWallet
            val activityId = userSBT.activityId
            if (activityId != null && !userTonAddress.isNullOrEmpty()) {
                val sbtReward =
                    sbtRewardRepo.getSBTByActivityId(activityId).firstOrNull { it.groupId in groupIdList }
                if (sbtReward != null) {
                    val sbtId = sbtReward.sbtId
                    val groupId = sbtReward.groupId
                    val userRewardClaimedType = participantRepo.getClaimTypeByUserId(sbtId, userId, groupId)
                    if (userRewardClaimedType in listOf(1, 2, 3)) {
                        val realType = wiseScoreService.getSBTClaimedStatus(activityId, userTonAddress)
                        println("$userTonAddress update $activityId && $sbtId claim type to $realType")
                        if (userRewardClaimedType == 1) {
                            addUserReward(3, sbtId, userId, groupId, realType)
                        }
                        sbtWhiteListRepo.updateUserSBTClaimedType(userId, userSBT.address ?: "", activityId, realType)
                    }
                }
            }
        }
    }


    fun luckyDrawPoint(
        group: CredentialGroup,
        verifiedUserNum: Int,
        limitedUserNum: Int,
        verifiedUsers: List<Long>
    ): List<Long> {
        val points = group.pointList
        val pointId = if (points.isNotEmpty()) points[0].pointId else 0
        val methodType = if (points.isNotEmpty()) points[0].methodType else 0
        val claimedUserNum = participantRepo.getClaimedUserCount(pointId, group.id, 4) // 单个group已经认证且claim的用户数量
        if (methodType == 2 && claimedUserNum < limitedUserNum) {
            val luckyUserList = luckyDraw(verifiedUserNum, limitedUserNum, verifiedUsers)
            for (luckyUser in luckyUserList) {
                addUserReward(2, pointId, luckyUser, group.id, 4)
            }
            return luckyUserList
        }
        return emptyList()
    }

    fun updateNftClaimedType(
        group: CredentialGroup, userId: Long, campaign: Campaign
    ): List<NFT> {
        println(" update claim type " + userId)
        val verifiedUserNum = getUserCountByGroupId(group.id) //单个group已经认证且claim的用户数量
        val nfts = group.nftList
        for (nft in nfts) {
            val isUnlimited = nft.unlimited
            val mintCap = nft.mintCap
            val claimedUserNum = getClaimedUserCountOfClaimType(nft.nftId, group.id, 4) // 单个group已经认证且claim nft的用户数量
            val methodType = nft.methodType
            var userClaimedType = 0
            if (participantRepo.getClaimTypeByUserId(nft.nftId, userId, group.id) != null) {
                userClaimedType = participantRepo.getClaimTypeByUserId(nft.nftId, userId, group.id)!!
            }
            val isClaimable = listOf(1, 3).contains(userClaimedType)
            var isNftAvailable = false
            if (isUnlimited) {
                isNftAvailable = true
            } else {
                isNftAvailable = claimedUserNum < mintCap
            }
            println("isNftAvailable " + isNftAvailable + "  isClaimable" + isClaimable + " status is " + campaign.status + " claimedType is " + nft.claimedType)
            if (isClaimable) {
                //todo
                if (campaign.status == CampaignStatus.COMPLETED && methodType == 1) { //
                    userClaimedType = 5
                }
                nft.claimedType = userClaimedType
            } else if (isNftAvailable) {
                if (methodType == 1) { //fcfs
                    nft.claimedType = 3 // claimable
                    addUserReward(1, nft.nftId, userId, group.id, 3)
                } else if (methodType == 2) { //lucky draw
                    if (campaign.status == CampaignStatus.COMPLETED && userClaimedType == 0) { //活动已结束，且nft为等待抽奖状态，userClaimedType == 0(未抽过奖)，开启抽奖
                        val verifiedUsers = getUserIdByGroupId(group.id) //单个group已经认证且claim的用户 id列表
                        val luckyList = luckyDraw(verifiedUserNum, mintCap.toInt(), verifiedUsers)
                        if (userId in luckyList) { //中奖
                            nft.claimedType = 3   //claimable
                            addUserReward(1, nft.nftId, userId, group.id, 3)
                        } else { //未中奖
                            nft.claimedType = 5   //missed未获取
                            addUserReward(1, nft.nftId, userId, group.id, 5)
                        }
                    } else if (campaign.status == CampaignStatus.COMPLETED && (nft.claimedType == 4 || nft.claimedType == 5)) {
                        //已经抽过奖
                    } else {
                        nft.claimedType = 1  //已完成等待抽奖
                    }
                }
            } else {
                nft.claimedType = 5 // missed
                addUserReward(1, nft.nftId, userId, group.id, 5)
            }
        }
        group.nftList = nfts
        return nfts
    }

    fun updateClaimType(rewardType: Int, rewardId: Long, userId: Long, groupId: Long, claimType: Int): CredentialGroup {
        addUserReward(rewardType, rewardId, userId, groupId, claimType)
        val group = credentialGroupService.getCredentialGroupById(groupId)!!
        val nfts = group.nftList
        for (nft in nfts) {
            if (nft.nftId == rewardId) {
                nft.claimedType == claimType
            }
        }
        group.nftList = nfts
        return group
    }

    fun addUserReward(rewardType: Int, rewardId: Long, userId: Long, groupId: Long, claimType: Int) {
        val userReward = UserReward(
            rewardId = rewardId, rewardType = rewardType, userId = userId, groupId = groupId, claimType = claimType
        )
        if (participantRepo.getClaimTypeByUserId(
                rewardId,
                userId,
                groupId
            ) != null
        ) {
            if (participantRepo.getClaimTypeByUserId(rewardId, userId, groupId) != claimType) {
                participantRepo.updateUserReward(userReward)
            }
        } else {
            println(" add reward " + userReward.claimType + " " + rewardId + " userId " + userId)
            participantRepo.addUserRewardResult(userReward)
        }
    }

    fun luckyDraw(verifiedUserNum: Int, limitedUserNum: Int, userIds: List<Long>): List<Long> {
        val random = Random(System.currentTimeMillis())
        if (limitedUserNum > userIds.size) {
            return userIds.shuffled(random).take(verifiedUserNum)
        }
        // 随机打乱 userIds 列表并取出 limitedUserNum 个元素
        val userList = userIds.shuffled(random).take(limitedUserNum)

        // 随机抽取指定数量的用户
        val luckyList = userList.shuffled(random).take(verifiedUserNum)
        // 返回抽取到的用户编号数组
        return luckyList
    }


    fun updateCampaign(campaignId: Long, userId: Long, address: String, totalPointNum: Long) {
        val userResult = participantRepo.getUserCampaignResult(userId, campaignId)
        if (userResult != null) return

        val userCampaign = UserCampaign(
            userId = userId, address = address, campaignId = campaignId, pointNum = totalPointNum
        )
        participantRepo.addUserCampaignResult(userCampaign)
    }

    fun updateUserGroup(groupId: Long, userId: Long, address: String, totalPointNum: Long) {
        val userResult = participantRepo.getUserGroupResult(userId, groupId)
        if (userResult != null) return

        val userGroup = UserGroup(
            userId = userId, address = address, groupId = groupId, pointNum = totalPointNum
        )
        participantRepo.addUserGroupResult(userGroup)
    }

    fun countCredentials(campaignTotal: CampaignTotal): Int {
        var totalCount = 0
        for (group in campaignTotal.groups) {
            totalCount += group.credentialList.size
        }
        return totalCount
    }

    fun countNfts(campaignTotal: CampaignTotal): Int {
        var totalCount = 0
        for (group in campaignTotal.groups) {
            totalCount += group.nftList.size
        }
        return totalCount
    }

    fun countPoints(campaignTotal: CampaignTotal): Int {
        var totalCount = 0
        for (group in campaignTotal.groups) {
            totalCount += group.pointList.size
        }
        return totalCount
    }

    fun mergeCredentialLists(campaignTotal: CampaignTotal): List<Credential> {
        return campaignTotal.groups.flatMap { it.credentialList }
    }

    fun mergeCredentialGroups(campaignTotals: List<CampaignTotal>): List<CredentialGroup> {
        return campaignTotals.flatMap { it.groups }
    }

    fun mergeNftLists(campaignTotal: CampaignTotal): List<NFT> {
        return campaignTotal.groups.flatMap { it.nftList }
    }

    fun mergePointLists(campaignTotal: CampaignTotal): List<Point> {
        return campaignTotal.groups.flatMap { it.pointList }
    }

    fun mergeSBTLists(campaignTotal: CampaignTotal): List<SBTReward> {
        return campaignTotal.groups.flatMap { it.sbtList }
    }

    fun mergeSuiSbtLists(campaignTotal: CampaignTotal): List<SuiSbtReward> {
        return campaignTotal.groups.flatMap { it.suiSbtList }
    }

//    fun getParticipantNumsByProjectId(projectId: Long): Int {
//        val campaigns = campaignRepo.getCampaignByProjectId(projectId)!!
//        val campaignIds = campaigns.map { it.campaignId }
//        var participantNum = 0
//        for (campaignId in campaignIds) {
//            val participants = participantRepo.getAllParticipants(campaignId)
//            val loggedInUsers = participantRepo.getAllLoggedInUsers(campaignId)
//            val userIdList = participants.distinctBy { it.userId }.map { it.userId }
//            val onlyLoggedInUserIds =
//                loggedInUsers.distinctBy { it.userId }.map { it.userId }.filter { it !in userIdList }
//            participantNum += userIdList.size + onlyLoggedInUserIds.size
//        }
//        return participantNum
//    }

    fun getParticipantNumsByCampaignIds(campaignIds: List<Long>): Map<Long, Long> {
        val keys = campaignIds.map { campaignId -> "campaign_participant:$campaignId" }
        val values = redisTemplate.opsForValue().multiGet(keys).orEmpty()
        logger.info { "getParticipantNumsByCampaignId, campaigns: $campaignIds, values: $values" }
        return campaignIds.zip(values).associate { (campaignId, value) ->
            campaignId to (value?.toLongOrNull() ?: 0L)
        }
    }

    fun getParticipantNumsByCampaignId(campaignId: Long): Int {
        val key = "campaign_participant:$campaignId"
        val value = redisTemplate.opsForValue().get(key)
        logger.info { "getParticipantNumsByCampaignId: $value" }
        return value?.toInt() ?: 0
    }

    fun getParticipantNumsByProjectId(projectId: Long): Int {
        val campaignIds = campaignRepo.getCampaignByProjectId(projectId).map { it.campaignId }
        val loggedInUserIdNum = participantRepo.getAllLoggedInUserIdNumByCampaignIds(campaignIds)
        return loggedInUserIdNum
    }

    fun getParticipationById(campaignId: Long): Participation {
//        val participantList = getParticipantById(campaignId)!!
        val campaignTotal = getCampaignTotalById(campaignId)!!

        val credentialList = mergeCredentialLists(campaignTotal)
        for (credential in credentialList) {
            val giveAway = participantRepo.getCredentialGiveAway(credential.credentialId).toLong()
            credential.giveAway = giveAway
        }

        val participantList = mutableListOf<Participant>()
        val participants = participantRepo.getAllParticipants(campaignId)
        val loggedInUsers = participantRepo.getAllLoggedInUsers(campaignId)
        val userIdList = participants.distinctBy { it.userId }.map { it.userId }
        val participantIdSet = userIdList.toSet()
        val onlyLoggedInUserIds =
            loggedInUsers.distinctBy { it.userId }.map { it.userId }.filter { it !in participantIdSet }
        val onlyLoggedInUsers = userRepo.getUsersByUserIdList(onlyLoggedInUserIds)!!
        val totalIdList = mutableListOf<Long>().apply {
            addAll(userIdList)
            addAll(onlyLoggedInUserIds)
        }
        val userIdAndEarliestParticipantDate = participants
            .groupBy { it.userId }
            .mapValues {
                it.value.minByOrNull { userCredential -> userCredential.participantDate!! }?.participantDate
            }
        val credentialIds =
            participants.groupBy { it.userId }.mapValues { it.value.map { participant -> participant.credentialId } }
        val uniqueCredentialIds = credentialIds.values.flatten().toSet()
        val credentialMap =
            uniqueCredentialIds.associateWith { credentialId -> credentialRepo.getCredentialById(credentialId) }

        val verifiedCredentials = credentialIds.map { (userId, credentialIds) ->
            userId to credentialIds.mapNotNull { credentialId -> credentialMap[credentialId] }
        }.toMap()

        val participantNum = totalIdList.size
        val credentialNum = participantRepo.getUserCredentialNum(campaignId)
        val nftNum = countNfts(campaignTotal)
        val nftList = mergeNftLists(campaignTotal)
        for (group in campaignTotal.groups) {
            for (nft in nftList) {
                val claimedCount = participantRepo.getClaimedUserCount(nft.nftId, group.id, 4)
                nft.claimedCount = claimedCount
            }
        }
        val twitterUsers = userTwitterService.getUsersInfo(totalIdList)
        val groupIds = campaignTotal.groups.map { it.id }
        val points = participantRepo.getRewardIdListByUserIdsAndGroupIds(userIdList, groupIds, 2, 4)
        val nfts = participantRepo.getRewardIdListByUserIdsAndGroupIds(userIdList, groupIds, 1, 4)
        val sbts = participantRepo.getRewardIdListByUserIdsAndGroupIds(userIdList, groupIds, 3, 4)
        val userGroups = participantRepo.getUserGroupResults(userIdList, groupIds)
        for (user in onlyLoggedInUsers) {
            val participant = loggedInUsers.first { it.userId == user.userId }
            participant.campaignId = campaignId
            if (participant.isTwitterLogin || user.wallet.isEmpty()) {
                //println("user Id is " + user.userId + " " + user.wallet)
                val twitterUser = twitterUsers.filter { it.userId == user.userId }
                if (twitterUser.isNotEmpty()) {
                    user.twitterName = twitterUser[0].twitterName
                }
                participant.isTwitterLogin = !user.twitterName.isNullOrEmpty()
                participant.twitterName = user.twitterName
            }
            if (telegramService.getUserInfo(user.userId)?.username != null) {
                participant.tgName = telegramService.getUserInfo(user.userId)!!.username
            }
            participantList.add(participant)
        }

        for (userId in userIdList) {
            val userList = participants.filter { it.userId == userId }
            var address = ""
            var isTwitterLogin = false
            var twitterName = ""
            var isTgLogin = false
            var tgName = ""
            if (userList.isNotEmpty()) {
                address = participants.filter { it.userId == userId }[0].address
                isTwitterLogin = participants.filter { it.userId == userId }[0].isTwitterLogin
            }
            if (address.isEmpty() || isTwitterLogin) {
                val twitterUser = twitterUsers.filter { it.userId == userId }
                if (twitterUser.isNotEmpty()) {
                    twitterName = twitterUser[0].twitterName
                }
                isTwitterLogin = twitterName.isNotEmpty()
            }
            if (address.isEmpty() && !isTwitterLogin) {
                tgName = userRepo.findUserById(userId)?.tgName ?: telegramService.getUserInfo(userId)?.username ?: ""
                if (tgName != "") {
                    isTgLogin = true
                }
            }
            val verifiedCredentialList = verifiedCredentials.getValue(userId)
//            val pointNum = participantRepo.getCampaignVerifiedUserPointById(userId, campaignId)
            var totalPointNum = 0L
            val userPointList = mutableListOf<Long>()
            val userNftList = mutableListOf<Long>()
            val userSBTList = mutableListOf<Long>()
            for (group in campaignTotal.groups) {
                val pointIds = points.filter { it.userId == userId && it.groupId == group.id }.map { it.rewardId }
                val nftIds = nfts.filter { it.userId == userId && it.groupId == group.id }.map { it.rewardId }
                val sbtIds = sbts.filter { it.userId == userId && it.groupId == group.id }.map { it.rewardId }
                userPointList.addAll(pointIds)
                userNftList.addAll(nftIds)
                userSBTList.addAll(sbtIds)
//                var pointNum = participantRepo.getGroupVerifiedUserPointById(userId, group.id)
//                var pointNum = userGroups.firstOrNull { it.userId == userId && it.groupId == group.id }?.pointNum
//                pointNum = participantRepo.getWinnersByRewardId()
                var isClaimed = false
                if (pointIds.isNotEmpty()) {
                    val pointNum = pointRepo.getPointByGroupId(group.id)[0].number
//                    isClaimed = participantRepo.getClaimTypeByUserId(pointIds[0], userId, group.id) == 4
                    totalPointNum += pointNum ?: 0

                }
//                pointNum = if (isClaimed) pointNum ?: 0 else 0
//                totalPointNum += pointNum
            }
            val firstDate = userIdAndEarliestParticipantDate.getValue(userId)
//            val firstParticipantDate =
//                participantRepo.getUserFirstParticipationDate(userId, campaignId)!! //获取用户认证credential成功的最早的时间\
            val participant = Participant(
                userId = userId,
                campaignId = campaignId,
                wallet = address,
                nfts = userNftList,
                points = userPointList,
                sbts = userSBTList,
                pointNum = totalPointNum ?: 0,
                credentials = credentialList,
                verifiedCredentials = verifiedCredentialList,
                participantDate = firstDate,
                isTwitterLogin = isTwitterLogin,
                twitterName = twitterName,
                isTgLogin = isTgLogin,
                tgName = tgName
            )
            participantList.add(participant)
        }
        val pointList = mergePointLists(campaignTotal)
        val sbtList = mergeSBTLists(campaignTotal)
        return Participation(
            campaignId = campaignId,
            participantNum = participantNum.toLong(),
            credentialNum = credentialNum,
            pointNum = participantList.sumOf { it.pointNum },
            nftNum = nftNum.toLong(),
            nftList = nftList,
            pointList = pointList,
            sbtList = sbtList,
            credentialList = credentialList,
            participantList = participantList.sortedByDescending { it.participantDate },
            projectId = campaignTotal.campaign.projectId
        )
    }

    fun getParticipationByIdV2(campaignId: Long, page: Int, limitNum: Int): Participation {
        val campaignTotal = getCampaignTotalById(campaignId)!!
        val credentialList = mergeCredentialLists(campaignTotal)
        for (credential in credentialList) {
            val giveAway = participantRepo.getCredentialGiveAway(credential.credentialId).toLong()
            credential.giveAway = giveAway
        }
        val participantList = mutableListOf<Participant>()
        val participants = participantRepo.getParticipantsByPageAndLimit(campaignId, page, limitNum)
        val participantIds = participantRepo.getAllParticipantIds(campaignId)
        val loggedInUserIds = participantRepo.getAllLoggedInUserIds(campaignId)
        val userIdList = participants.distinctBy { it.userId }.map { it.userId }
        val participantIdSet = participantIds.toSet()
        val onlyLoggedInUserIds = loggedInUserIds.filter { it !in participantIdSet }
//        val onlyLoggedInUsers = userRepo.getUsersByUserIdList(onlyLoggedInUserIds)!!
        val totalIdList = mutableListOf<Long>().apply {
            addAll(participantIds)
            addAll(onlyLoggedInUserIds)
        }
        val userIdAndEarliestParticipantDate = participants
            .groupBy { it.userId }
            .mapValues {
                it.value.minByOrNull { userCredential -> userCredential.participantDate!! }?.participantDate
            }
        val credentialIds =
            participants.groupBy { it.userId }.mapValues { it.value.map { participant -> participant.credentialId } }
        val uniqueCredentialIds = credentialIds.values.flatten().toSet()
        val credentialMap =
            uniqueCredentialIds.associateWith { credentialId -> credentialRepo.getCredentialById(credentialId) }
        val verifiedCredentials = credentialIds.map { (userId, credentialIds) ->
            userId to credentialIds.mapNotNull { credentialId -> credentialMap[credentialId] }
        }.toMap()
        val participantNum = totalIdList.size
        val credentialNum = participantRepo.getUserCredentialNum(campaignId)
        val nftNum = countNfts(campaignTotal)
        val nftList = mergeNftLists(campaignTotal)
        var totalPointNum = 0L
        for (group in campaignTotal.groups) {
            for (nft in nftList) {
                val claimedCount = participantRepo.getClaimedUserCount(nft.nftId, group.id, 4)
                nft.claimedCount = claimedCount
            }
            for (point in group.pointList) {
                val pointClaimedCount = participantRepo.getClaimedUserCount(point.pointId, group.id, 4)
                val pointNum = pointClaimedCount * point.number
                totalPointNum += pointNum
            }
        }
        val twitterUsers = userTwitterService.getUsersInfo(userIdList)
        val groupIds = campaignTotal.groups.map { it.id }
        val points = participantRepo.getRewardIdListByUserIdsAndGroupIds(userIdList, groupIds, 2, 4)
        val nfts = participantRepo.getRewardIdListByUserIdsAndGroupIds(userIdList, groupIds, 1, 4)
        val sbts = participantRepo.getRewardIdListByUserIdsAndGroupIds(userIdList, groupIds, 3, 4)
        val suiSbts = participantRepo.getRewardIdListByUserIdsAndGroupIds(userIdList, groupIds, 4, 4)
//        val userGroups = participantRepo.getUserGroupResults(userIdList, groupIds)
//        for (user in onlyLoggedInUsers) {
//            val participant = loggedInUsers.first { it.userId == user.userId }
//            participant.campaignId = campaignId
//            if (participant.isTwitterLogin || user.wallet.isEmpty()) {
//                //println("user Id is " + user.userId + " " + user.wallet)
//                val twitterUser = twitterUsers.filter { it.userId == user.userId }
//                if (twitterUser.isNotEmpty()) {
//                    user.twitterName = twitterUser[0].twitterName
//                }
//                participant.isTwitterLogin = !user.twitterName.isNullOrEmpty()
//                participant.twitterName = user.twitterName
//            }
//            if (telegramService.getUserInfo(user.userId)?.username != null) {
//                participant.tgName = telegramService.getUserInfo(user.userId)!!.username
//            }
//            participantList.add(participant)
//        }

        for (userId in userIdList) {
            val userList = participants.filter { it.userId == userId }
            var address = ""
            var isTwitterLogin = false
            var twitterName = ""
            var isTgLogin = false
            var tgName = ""
            val suiAddress = userRepo.findUserById(userId)?.suiAddress ?: ""
            if (userList.isNotEmpty()) {
                address = participants.filter { it.userId == userId }[0].address
                isTwitterLogin = participants.filter { it.userId == userId }[0].isTwitterLogin
            }
            if (address.isEmpty() || isTwitterLogin) {
                val twitterUser = twitterUsers.filter { it.userId == userId }
                if (twitterUser.isNotEmpty()) {
                    twitterName = twitterUser[0].twitterName
                }
                isTwitterLogin = twitterName.isNotEmpty()
            }
            if (address.isEmpty() && !isTwitterLogin) {
                tgName = userRepo.findUserById(userId)?.tgName ?: telegramService.getUserInfo(userId)?.username ?: ""
                if (tgName != "") {
                    isTgLogin = true
                }
            }
            if (!isTgLogin && address.isEmpty() && suiAddress.isNotEmpty()) {
                address = suiAddress
            }
            val verifiedCredentialList = verifiedCredentials.getValue(userId)
//            val pointNum = participantRepo.getCampaignVerifiedUserPointById(userId, campaignId)
            var userTotalPointNum = 0L
            val userPointList = mutableListOf<Long>()
            val userNftList = mutableListOf<Long>()
            val userSBTList = mutableListOf<Long>()
            val userSuiSBTList = mutableListOf<Long>()
            for (group in campaignTotal.groups) {
                val pointIds = points.filter { it.userId == userId && it.groupId == group.id }.map { it.rewardId }
                val nftIds = nfts.filter { it.userId == userId && it.groupId == group.id }.map { it.rewardId }
                val sbtIds = sbts.filter { it.userId == userId && it.groupId == group.id }.map { it.rewardId }
                val suiSbtIds = suiSbts.filter { it.userId == userId && it.groupId == group.id }.map { it.rewardId }
                userPointList.addAll(pointIds)
                userNftList.addAll(nftIds)
                userSBTList.addAll(sbtIds)
                userSuiSBTList.addAll(suiSbtIds)
                var isClaimed = false
                if (pointIds.isNotEmpty()) {
                    println("pointsIds is ${pointIds[0]}")
                    val pointNum = group.pointList[0].number
                    println("pointsNum is $pointNum")
                    userTotalPointNum += pointNum
                }
                println("userTotalPointNum is $userTotalPointNum")
            }
            val firstDate = userIdAndEarliestParticipantDate.getValue(userId)
            val participant = Participant(
                userId = userId,
                campaignId = campaignId,
                wallet = address,
                nfts = userNftList,
                points = userPointList,
                sbts = userSBTList,
                suiSbts = userSuiSBTList,
                pointNum = userTotalPointNum,
                credentials = credentialList,
                verifiedCredentials = verifiedCredentialList,
                participantDate = firstDate,
                isTwitterLogin = isTwitterLogin,
                twitterName = twitterName,
                isTgLogin = isTgLogin,
                tgName = tgName
            )
            participantList.add(participant)
        }
        val pointList = mergePointLists(campaignTotal)
        val sbtList = mergeSBTLists(campaignTotal)
        val suiSbtList = mergeSuiSbtLists(campaignTotal)
        var sbtClaimedNum = 0L
        if (sbtList.isNotEmpty()) {
            val sbtActivityId = sbtList[0].activityId
            sbtClaimedNum = sbtWhiteListRepo.getClaimedUserCntByActivityId(sbtActivityId)
        }
        var suiSbtClaimedNum = 0L
        if (suiSbtList.isNotEmpty()) {
            for (suiSbt in suiSbtList) {
                val claimedNum = participantRepo.getClaimedUserCountByRewardId(suiSbt.suiSbtId)
                suiSbt.holderCnt = claimedNum
                suiSbtClaimedNum += claimedNum
            }
        }

        return Participation(
            campaignId = campaignId,
            participantNum = participantNum.toLong(),
            credentialNum = credentialNum,
//            pointNum = participantList.sumOf { it.pointNum },
            pointNum = totalPointNum,
            nftNum = nftNum.toLong(),
            sbtClaimedNum = sbtClaimedNum + suiSbtClaimedNum,
            nftList = nftList,
            pointList = pointList,
            sbtList = sbtList,
            suiSbtList = suiSbtList,
            credentialList = credentialList,
            participantList = participantList.sortedByDescending { it.participantDate },
            projectId = campaignTotal.campaign.projectId
        )
    }

    fun getCampaignRewardById(campaignId: Long): CampaignRewardWithWinner {
        val campaignTotal = getCampaignTotalById(campaignId)!!
        val campaignStatus = updateCampaignStatus(campaignTotal)?.campaign?.status!!
        val groups = credentialGroupService.getCredentialGroupByCampaignId(campaignId)!!
        var campaignNftWithWinnerList = mutableListOf<CampaignNftWithWinner>()
        var campaignPointWithWinnerList = mutableListOf<CampaignPointWithWinner>()
        for (group in groups) {
            val groupId = group.id
            val points = group.pointList
            val nfts = group.nftList
            for (nft in nfts) {
                var winners = participantRepo.getWinnersByRewardId(nft.nftId, groupId)
                for (winner in winners) {
                    val user = userRepo.findUserById(winner.userId)!!
                    winner.user = user
                }
                val nftWithWinner = CampaignNftWithWinner(
                    nft = nft, status = campaignStatus, winnerList = winners
                )
                campaignNftWithWinnerList.add(nftWithWinner)
            }
            for (point in points) {
                var winners = participantRepo.getWinnersByRewardId(point.pointId, groupId)
                for (winner in winners) {
                    val user = userRepo.findUserById(winner.userId)!!
                    println("wallet is " + user.twitterName)
                    winner.user = user
                }
                val pointWithWinner = CampaignPointWithWinner(
                    point = point, status = campaignStatus, winnerList = winners
                )
                campaignPointWithWinnerList.add(pointWithWinner)
            }
        }
        return CampaignRewardWithWinner(
            campaignId = campaignId, nfts = campaignNftWithWinnerList, points = campaignPointWithWinnerList
        )

    }

    fun getWinnersByRewardAndGroupId(rewardId: Long, groupId: Long): List<UserReward> {
        return participantRepo.getWinnersByRewardId(rewardId, groupId)
    }

//    fun mockParticipants(): List<Participant> {
//        val nft1 = NFT(
//            nftId = 1,
//            name = "My NFT 1",
//            symbol = "NFT1",
//            contract = "0x1234567890",
//            chainId = 1,
//            coverUrl = "https://example.com/nft1.png",
//            projectId = 1,
//            creatorId = 1,
//            groupId = 1
//        )
//
//        val nft2 = NFT(
//            nftId = 2,
//            name = "My NFT 2",
//            symbol = "NFT2",
//            contract = "0x0987654321",
//            chainId = 1,
//            coverUrl = "https://example.com/nft2.png",
//            projectId = 2,
//            creatorId = 2,
//            groupId = 2
//        )
//
//        val point1 = Point(1, 2, 1, true, 1, 222, 333, 4444)
//        val point2 = Point(2, 2, 1, true, 1, 222, 333, 4444)
//
//        val followerUser = "test_user"
//        val retweetUser = "re_user"
//
//
//        val followCredential = Credential(
//            0,
//            "Follow on Twitter",
//            "Follow <%= $followerUser %> on Twitter",
//            1,
//            "",
//            "https://rd-worker.xgamma.workers.dev/img/********************************",
//            0,
//            0,
//            0,
//            2,
//            "",
//            0
//        )
//        val retweetCredential = Credential(
//            1,
//            "Twitter Retweet",
//            "Retweet <%= $retweetUser %> on Twitter",
//            1,
//            "",
//            "https://rd-worker.xgamma.workers.dev/img/********************************",
//            0,
//            0,
//            0,
//            2,
//            "",
//            0
//        )

//        val participant_1 = Participant(
//            userId = 222374140488,
//            campaignId = 188029660202,
//            wallet = "******************************************",
//            nfts = listOf<NFT>(nft1, nft2),
//            points = listOf<Point>(point1, point2),
//            pointNum = 2,
//            isJoin = true,
//            isVisit = true,
//            credentials = listOf<Credential>(followCredential, retweetCredential),
//            verifiedCredentials = listOf<Credential>(followCredential, retweetCredential),
//            participantDate = Instant.now()
//        )
//        val participant_2 = Participant(
//            userId = 217477080363,
//            campaignId = 188029660202,
//            wallet = "******************************************",
//            nfts = listOf<NFT>(nft1, nft2),
//            points = listOf<Point>(point1, point2),
//            pointNum = 2,
//            isJoin = true,
//            isVisit = true,
//            credentials = listOf<Credential>(followCredential, retweetCredential),
//            verifiedCredentials = listOf<Credential>(followCredential),
//            participantDate = Instant.now()
//        )
//        val participant_3 = Participant(
//            userId = 210235980357,
//            campaignId = 188029660202,
//            wallet = "******************************************",
//            nfts = listOf<NFT>(nft1, nft2),
//            points = listOf<Point>(point1, point2),
//            pointNum = 2,
//            isJoin = true,
//            isVisit = true,
//            credentials = listOf<Credential>(followCredential, retweetCredential),
//            verifiedCredentials = listOf<Credential>(retweetCredential),
//            participantDate = Instant.now()
//        )
//        return listOf<Participant>(participant_1, participant_2, participant_3)

//    }


    fun getCampaignByProjectId(projectId: Long): List<Campaign> {
        return campaignRepo.getCampaignByProjectId(projectId)
    }

    fun getCampaignByCreatorId(creatorId: Long): List<Campaign> {
        return campaignRepo.getCampaignByCreatorId(creatorId)
    }

    fun getCampaignByStats(id: Long): CampaignStats {
        val campaign = campaignRepo.getCampaignById(id)!!
        val credentialStats = resultReportRepo.getCampaignCredentialStats(id)
        val cmStats = resultReportRepo.getCampaignUserStats(id)
        val credentials = credentialRepo.getCredentialByProjectId(campaign.projectId).associateBy { it.credentialId }
        val nft = nftRepository.getNFTById(155013710015)
        val attendees = credentialStats.map { c ->
            val points = cmStats[c.key]?.sum() ?: 0
            val creds = c.value.mapNotNull { cv -> credentials[cv] }
            Attendee(c.key, points, creds, nft!!)
        }
        return CampaignStats(campaign, credentials.values.toList(), attendees)
    }

    fun getCampaignPoints(id: Long): List<AddressPoint> {
        return resultReportRepo.getCampaignPoints(id).map { AddressPoint(it.key, it.value) }
    }

    @Transactional
    fun updateCampaign(campaign: Campaign): Campaign {
        val rewardRequest = mapper.readValue<List<CampaignReward>>(campaign.reward)
        campaignRepo.updateCampaign(campaign)
        campaignRepo.deleteCampaignCredentials(campaign.campaignId)
        campaignRepo.addCampaignCredential(campaign.campaignId,
            rewardRequest.flatMap { it.credentials }.map { it.toLong() to idGenerator.getNewId() })
        return campaignRepo.getCampaignById(campaign.campaignId)!!
    }

    fun getAllScheduledAndOnGoingCampaigns(): List<Campaign>? {
        return campaignRepo.getAllScheduledAndOnGoingCampaigns()
    }

    fun getPointsClaimedCnt(): Long {
        return participantRepo.getPointsClaimedCnt()
    }

    fun getPointsClaimedCntFromRedis(): Long {
        val key = "total_points_claimed_count:"
        val v = redisTemplate.opsForValue().get(key)
        if (v == null) {
            logger.info { "EMPTY result from $key" }
            return 0
        }
        logger.info { "total_points_claimed_count : $v" }
        return v.toLong()
    }


    fun getUserIdByGroupId(groupId: Long): List<Long> {
        val gk = "credential_group_users:${groupId}"
        val scanOptions = ScanOptions.scanOptions().match("*").count(100).build()
        val v = redisTemplate.opsForSet().scan(gk, scanOptions)
        val result = mutableListOf<Long>()
        v.use {
            it.forEachRemaining { v -> result.add(v.toLong()) }
        }
        logger.info { "user ids from group $groupId, count: ${result.size}" }
        return result
    }

    fun getUserCountByGroupId(groupId: Long): Int {
        val gk = "credential_group_users:${groupId}"
        val v = redisTemplate.opsForSet().size(gk)
        if (v == null) {
            logger.info { "EMPTY users from group $groupId" }
            return 0
        }
        logger.info { "user count from group $groupId, count $v" }
        return v.toInt()
    }

    fun getClaimedUserCountOfClaimType(rewardId: Long, groupId: Long, claimType: Int): Int {
        val key = "reward_user_count:${rewardId}_${groupId}_${claimType}"
        val v = redisTemplate.opsForValue().get(key)
        if (v == null) {
            logger.info { "EMPTY users from group $key" }
            return 0
        }
        logger.info { "user count for reward group type $key, count $v" }
        return v.toInt()
    }

    fun getCampaignTVL(credentialId: Long): List<WiseScoreService.StakeInfo> {

        val credentialList = participantRepo.getUserCredentialsByCredentialId(credentialId)
            .filter { it.amount4 == 0L }
        val labelType = credentialList[0].labelType

        val addressList = credentialList.map { credential ->
            val uid = credential.userId
            val tonAddress = userRepo.findUserById(uid)?.ton?.tonWallet ?: credential.address
            TVLAddressInfo(
                address = tonAddress,
                userId = credential.userId
            )
        }.toTypedArray()

        println("address List size is" + addressList.size)

        // 2024.9.10 09:00 (UTC) - begin time of late night defi
//        val lateNightDefiStartDate = 1725926400
        val stakeInfoList =
            wiseScoreService.getUserStakeEventsAmount(addressList, credentialId, labelType)
        return stakeInfoList
    }
}