package com.rewardoor.app.services

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.node.ObjectNode
import com.google.common.hash.Hashing
import com.pengrad.telegrambot.TelegramBot
import com.pengrad.telegrambot.model.ChatFullInfo
import com.pengrad.telegrambot.model.ChatMember
import com.pengrad.telegrambot.request.*
import com.pengrad.telegrambot.response.UserChatBoostsResponse
import com.rewardoor.app.dao.ParticipantRepository
import com.rewardoor.app.dao.TelegramUserRepository
import com.rewardoor.app.dao.UserRepository
import com.rewardoor.app.dao.tables.TBUserTelegram
import com.rewardoor.app.dao.tables.TgPrivateGroupRepository
import com.rewardoor.app.dto.TgAuthCallbackReq
import com.rewardoor.app.utils.TgStarter
import com.rewardoor.model.Participant
import com.rewardoor.model.TgPrivateGroupInfo
import com.rewardoor.model.User
import com.rewardoor.model.UserTelegramInfo
import org.apache.http.client.utils.URLEncodedUtils
import org.jetbrains.exposed.sql.select
import org.springframework.beans.factory.annotation.Value
import org.springframework.data.redis.core.StringRedisTemplate
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.net.URI
import java.security.MessageDigest
import java.time.Duration
import java.time.Instant
import java.util.*
import java.util.regex.Pattern
import javax.crypto.Mac
import javax.crypto.spec.SecretKeySpec


@Service
@Transactional
class TelegramService(
    @Value("\${tg.check_bot_token}") val tgToken: String,
    val telegramUserRepository: TelegramUserRepository,
    val userRepo: UserRepository,
    val participantRepo: ParticipantRepository,
    val idGenerator: DbIdGenerator,
    val redisTemplate: StringRedisTemplate,
    val tgPrivateGroupRepo: TgPrivateGroupRepository,
    @Value("\${wise_task.channel}") val tgChannel: String,
    @Value("\${tg.mini_app.bot_name}") private val botName: String,
    @Value("\${tg.mini_app.token}") private val miniAppBotToken: String,
    @Value("\${tg.mini_app.bot_name}") private val miniAppBotName: String,
    ) {
    private val mapper = ObjectMapper()
    private val log = mu.KotlinLogging.logger {}
    val tgBot = TelegramBot(tgToken)
    val miniAppBot = TelegramBot(miniAppBotToken)
    val TG_URL_START_COUNTER = "tg:start:url:counter"
    val miniAppBotUrl = "https://t.me/$botName"

    fun verifyHash(auth: String): Boolean {
        val authObj = mapper.readTree(auth) as ObjectNode
        return verifyHash(authObj)
    }

    fun verifyHash(authObj: ObjectNode): Boolean {
        val hash = authObj.get("hash").asText()
        authObj.remove("hash")
        val paramMap = authObj.properties().associate { it.key to it.value.asText() }
        return verifyHash(paramMap, hash, tgToken)
    }

    fun getTgUserCnt(): Long {
        return telegramUserRepository.getTgUserCnt()
    }

    fun getUserInfo(userId: Long): UserTelegramInfo? {
        return telegramUserRepository.getTgUser(userId)
    }

    fun getUserIdList(): List<Long> {
        return telegramUserRepository.getTgUserIdList()
    }

    fun updateTgUserId(userId: Long, newUserId: Long) {
        return telegramUserRepository.updateTgUserId(userId, newUserId)
    }

    fun getUserInfoByTgId(tgId: Long): UserTelegramInfo? {
        return telegramUserRepository.getTgUserByTgId(tgId)
    }

    fun addUserTgInfo(userId: Long, tgReq: TgAuthCallbackReq) {
        if (getUserInfo(userId) != null) {
            telegramUserRepository.updateTgUser(userId, tgReq)
        } else {
            telegramUserRepository.addTgUser(userId, tgReq)
        }
    }

    fun registerTgUser(tgReq: TgAuthCallbackReq): Long {
        val current = telegramUserRepository.getTgUserByTgId(tgReq.id)
        if (current != null) {
            return current.userId
        }

        val newId = idGenerator.getNewId()
        userRepo.addUser(
            User(
                newId,
                "https://api.dicebear.com/7.x/fun-emoji/svg?backgroundColor=b6e3f4,c0aede,d1d4f9,f6d594,fcbc34,ffd5dc,ffdfbf&backgroundType=gradientLinear&eyes=closed,closed2&mouth=lilSmile,wideSmile&seed=${newId}",
                "",
                ""
            ), newId
        )
        telegramUserRepository.addTgUser(newId, tgReq)
        val participant = Participant(
            userId = newId,
            campaignId = 0L,
            wallet = "",
            nfts = emptyList(),
            points = emptyList(),
            pointNum = 0L,
            isJoin = false,
            isVisit = false,
            credentials = emptyList(),
            verifiedCredentials = emptyList(),
            participantDate = Instant.now()
        )
        participantRepo.createParticipant(participant)
        return newId
    }

    fun verifyUserInChannel(userId: Long, channelId: String): Boolean {
        val tgInfo = getUserInfo(userId) ?: return false
        val member = tgBot.execute(GetChatMember(channelId, tgInfo.tgId)).chatMember()
        log.info("Get chat member: userId: {}, channelId: {}, result: {}", userId, channelId, member)

        if (member == null) return false
        return member.status() == ChatMember.Status.member
                || member.status() == ChatMember.Status.administrator
                || member.status() == ChatMember.Status.creator
                || member.status() == ChatMember.Status.restricted
    }

    fun getTgInfo(url: String): ChatFullInfo? {
        val lp = URI(url).path.split("/").last()
        val id = if (lp.startsWith("@")) lp else "@${lp}"
        return tgBot.execute(GetChat(id)).chat()
    }

    fun getTgMemberCountByUrl(url: String): Int {
        val group = URI(url).path.split("/").last()
        return getTgMemberCount(group)
    }

    fun getTgMemberCount(group: String): Int {
        val id = if (group.startsWith("@")) group else "@${group}"
        try {
            return tgBot.execute(GetChatMemberCount(id)).count()
        } catch (e: Exception) {
            log.error("getTgMemberCount error", e)
            return 0
        }
    }

    fun isUserAdministratorByUrl(userId: Long, url: String): Boolean {
//        val privateLink = extractPrivateLink(url)
//        if (privateLink != null) {
//            return isUserAdminByChatId(userId, privateLink)
//        }
        val group = URI(url).path.split("/").last()
        return isUserAdministrator(userId, group)
    }

    fun isUserAdminByChatId(userId: Long, privateLink: String): Boolean {
        val tgInfo = getUserInfo(userId) ?: return false
        val info = getTgPrivateGroupInfoByHash(privateLink) ?: return false
        val member = tgBot.execute(GetChatMember(info.tgId, tgInfo.tgId)).chatMember() ?: return false
        return member.status() == ChatMember.Status.administrator
                || member.status() == ChatMember.Status.creator
    }

    fun isUserAdministrator(userId: Long, group: String): Boolean {
        val tgInfo = getUserInfo(userId) ?: return false
        val id = if (group.startsWith("@")) group else "@${group}"
        val member = tgBot.execute(GetChatMember(id, tgInfo.tgId)).chatMember() ?: return false
        return member.status() == ChatMember.Status.administrator
                || member.status() == ChatMember.Status.creator
    }

    fun getInfoByUrl(url: String): Map<String, Any> {
        try {
            val chat =
                getTgInfo(url) ?: return mapOf("link" to url, "code" to 4001, "message" to "Invalid telegram link")
            return mapOf(
                "code" to 200,
                "message" to "OK",
                "data" to mapOf("link" to url, "telegramGroup" to chat.title(), "telegramChannel" to chat.title())
            )
        } catch (e: Exception) {
            log.error(e) { "Error getting telegram info from url $url" }
            return mapOf("link" to url, "code" to 4001, "message" to "Error getting telegram info from $url")
        }
    }

    fun generateOneTimeToken(userId: Long): String {
        val token = UUID.randomUUID().toString()
        redisTemplate.opsForValue().set("tg-ot-token:$token", userId.toString())
        return Base64.getUrlEncoder().encodeToString(token.toByteArray())
    }

    fun verifyOneTimeToken(ot: String): Long? {
        val token = String(Base64.getUrlDecoder().decode(ot))
        return redisTemplate.opsForValue().getAndDelete("tg-ot-token:$token")?.toLong()
    }

    fun generateBindToken(userId: Long): String {
        val token = UUID.randomUUID().toString().replace("-", "")
        redisTemplate.opsForValue().set("tg-bind-token:$token", userId.toString(), Duration.ofMinutes(30))
        return token
    }

    fun verifyBindToken(token: String): Long? {
        return redisTemplate.opsForValue().getAndDelete("tg-bind-token:$token")?.toLong()
    }

    fun addTgPrivateGroupInfo(info: TgPrivateGroupInfo) {
        tgPrivateGroupRepo.addTgPrivateGroup(info)
    }

    fun updateTgPrivateGroupInfo(info: TgPrivateGroupInfo) {
        tgPrivateGroupRepo.updateTgPrivateGroup(info)
    }

    fun getTgPrivateGroupInfoByHash(hash: String): TgPrivateGroupInfo? {
        return tgPrivateGroupRepo.getTgPrivateGroupInfoByHash(hash)
    }

    fun getTgPrivateGroupInfoByTgId(tgId: Long): TgPrivateGroupInfo? {
        return tgPrivateGroupRepo.getTgPrivateGroupInfoByTgId(tgId)
    }

    fun verifyUserBoostInChannel(userId: Long, chatId: String): Boolean {
        val tgInfo = getUserInfo(userId) ?: return false
        val boosts = tgBot.execute(GetUserChatBoosts(chatId, tgInfo.tgId))
        log.info("Get user chat boosts: userId: {}, chatId: {}, result: {}", userId, chatId, boosts)

        if (boosts == null) return false
        return boosts.boosts().isNotEmpty()
    }

    fun getByMiniAppBot(tgId: Long): com.pengrad.telegrambot.model.User? {
        val member = tgBot.execute(GetChatMember("@$miniAppBotName", tgId))
        log.info("Get user by miniapp, tgId [{}], result [{}]", tgId, member)
        return member?.chatMember()?.user()
    }

    fun getUserTgInfo(userId: Long): com.pengrad.telegrambot.model.User? {
        val tgInfo = getUserInfo(userId) ?: return null
        val memberByMiniApp = getByMiniAppBot(tgInfo.tgId)
        if (memberByMiniApp != null) {
            return memberByMiniApp
        }
        val member = tgBot.execute(GetChatMember("@$tgChannel", tgInfo.tgId))
        log.info("Get user chat member, userId [{}], result [{}]", userId, member)
        return member?.chatMember()?.user()
    }

    fun getUserInfos(userIds: List<Long>): List<UserTelegramInfo> {
        return telegramUserRepository.getTgUsers(userIds)
    }

    companion object {
        fun verifyHash(paramMap: Map<String, String>, hash: String, token: String): Boolean {
            val joinString =
                paramMap.entries.sortedBy { it.key }.joinToString(separator = "\n") { "${it.key}=${it.value}" }

            val sk = SecretKeySpec( // Get SHA 256 from telegram token
                MessageDigest.getInstance("SHA-256").digest(token.toByteArray()), "HmacSHA256"
            )
            val mac = Mac.getInstance("HmacSHA256")
            mac.init(sk)
            val result = mac.doFinal(joinString.toByteArray())
            val resultStr = HexFormat.of().formatHex(result)
            return resultStr.equals(hash, true)
        }

        fun verifyMiniApp(payload: String, token: String): Pair<Boolean, String> {
            val args = URLEncodedUtils.parse(payload, Charsets.UTF_8).associate { it.name to it.value }
            val hash = args["hash"] ?: return false to ""
            val pureArgs = args.toMutableMap()
            pureArgs.remove("hash")

            val joinString =
                pureArgs.entries.sortedBy { it.key }.joinToString(separator = "\n") { "${it.key}=${it.value}" }
            val h1 = Hashing.hmacSha256("WebAppData".toByteArray()).hashBytes(token.toByteArray())
            val h2 = Hashing.hmacSha256(h1.asBytes()).hashBytes(joinString.toByteArray()).asBytes()
            val resultStr = HexFormat.of().formatHex(h2)

            val result = resultStr.equals(hash, true)
            return result to args["user"].orEmpty()
        }

        val pattern = Pattern.compile("(?:t|telegram)\\.(?:me|dog)/(joinchat/|\\+)([\\w-]+)", Pattern.MULTILINE)

        fun extractPrivateLink(link: String): String? {
            val m = pattern.matcher(link)
            if (m.find()) {
                return m.group(2)
            }
            return null
        }
    }
}

class GetUserChatBoosts(chatId: Any, userId: Any) :
    BaseRequest<GetUserChatBoosts, UserChatBoostsResponse>(UserChatBoostsResponse::class.java) {
    init {
        add("chat_id", chatId).add("user_id", userId)
    }
}
