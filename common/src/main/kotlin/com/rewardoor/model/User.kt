package com.rewardoor.model

open class User(
    val userId: Long,
    var avatar: String,
    var email: String,
    var name: String,
    var newUser: Boolean = false,
    var projects: List<Project> = emptyList(),
    var evm: UserEvm = UserEvm(userId, null),
    var ar: UserAr = UserAr(userId, null, null),
    var zk: UserZK = UserZK(userId, null, null, null, null, null),
    var ton: UserTon = UserTon(userId, null, null),
    var sui: UserSui = UserSui(userId, null, null),
    var isTwitterLogin: Boolean = false,
    var twitterName: String = "",
    var dcName: String = "",
    var tgName: String = "",
    var hasWiseScore: Boolean = false
) {
    val wallet: String
        get() = evm.evmWallet ?: ""
    val arAddress: String get() = ar.arWallet ?: ""
    val suiAddress: String get() = sui.suiWallet ?: ""
    val zkAddress: String get() = zk.address ?: "'"

    val displayAvatar: String
        get() {
            return "https://api.dicebear.com/7.x/fun-emoji/svg?seed=${userId}&radius=50&backgroundColor=059ff2,fcbc34,d84be5,f6d594,ffd5dc,ffdfbf,d1d4f9,c0aede,b6e3f4&backgroundType=gradientLinear&backgroundRotation=30,60&eyes[]&mouth[]"
        }
}

class UserEvm(
    val userId: Long,
    var evmWallet: String?
) {
    val binded: Boolean
        get() = evmWallet != null
}

class UserAr(
    val userId: Long,
    val arWallet: String?,
    val publicKey: String?
) {
    val binded: Boolean
        get() = arWallet != null
}

class UserTon(
    val userId: Long,
    val tonWallet: String?,
    val publicKey: String?
) {
    val binded: Boolean
        get() = tonWallet != null
}

class UserSui(
    val userId: Long,
    val suiWallet: String?,
    val publicKey: String?
) {
    val binded: Boolean
        get() = suiWallet != null
}

class UserZK(
    var userId: Long,
    var issuer: String?,
    var sub: String?,
    var identity: String?,
    var address: String?,
    var salt: String?,
    var newUser: Boolean = false
) {
    val binded: Boolean
        get() = address != null

    fun displayIssuer(): String {
        return getDisplayIssuer(issuer)
    }

    companion object {
        fun getDisplayIssuer(issuer: String?): String {
            if (issuer == null) return ""
            if (issuer.indexOf("google.com") >= 0) return "Google"
            if (issuer.indexOf("facebook.com") >= 0 || issuer.indexOf("meta") >= 0) return "Meta"
            return ""
        }
    }
}
