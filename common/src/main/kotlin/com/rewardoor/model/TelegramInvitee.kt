package com.rewardoor.model

data class TelegramInvitee(
    private val tgId: Long,
    private val firstName: String,
    private val lastName: String,
    private val username: String,
    val premium: <PERSON><PERSON>an
) {
    val tgName: String
        get() = "$firstName $lastName"
    val avatar: String
        get() = "https://api.dicebear.com/7.x/fun-emoji/svg?seed=${tgId}&radius=50&backgroundColor=059ff2,fcbc34,d84be5,f6d594,ffd5dc,ffdfbf,d1d4f9,c0aede,b6e3f4&backgroundType=gradientLinear&backgroundRotation=30,60&eyes[]&mouth[]"
}