package com.rewardoor.model

import java.time.Instant

class AirDrop(
    val airDropId: Long,
    val chainId: Int = 0, // -1 : ton
    val chainName: String = "",
    val projectName: String,
    val projectDesc: String = "",
    val socialLink: String = "",
    val airDropDesc: String = "",

    val tokenName: String,
    val tokenContractAddress: String = "",
    val airdropContractAddress: String = "",
    val gas: String = "",
    val status: Int = 0, // 0 - not started, 1 - ongoing , 2 - ended
    val startTime: Instant? = null,
    val endTime: Instant? = null
) {
}