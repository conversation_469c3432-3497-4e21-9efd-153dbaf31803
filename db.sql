CREATE TABLE tb_sequence
(
    id           BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    place_holder VARCHAR(10) NOT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY (`place_holder`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

create table if not exists tb_user
(
    id int not null auto_increment,
    user_id bigint not null,
    name varchar(255) not null,
    email varchar(255) not null,
    wallet_address varchar(255) not null default '',
    ar_address varchar(255) not null default '',
    avatar varchar(255) not null default '',
    created_at datetime not null default current_timestamp,
    updated_at datetime not null default current_timestamp on update current_timestamp,
    primary key (id),
    unique key uk_user_id (user_id)
) engine=InnoDB default charset=utf8mb4;

CREATE TABLE `tb_project_admin` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `user_id` BIGINT NOT NULL,
    `wallet_address` VARCHAR(128) NOT NULL,
    `project_id` BIGINT NOT NULL,
    `creator_id` BIGINT NOT NULL,
    `is_owner` INT NOT NULL,
    `status` INT NOT NULL,
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `wallet_project_id_key` (`wallet_address`,`project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

create table if not exists tb_user_evm(
    id int not null auto_increment,
    user_id bigint not null,
    address varchar(128) not null,
    created_at datetime not null default current_timestamp,
    updated_at datetime not null default current_timestamp on update current_timestamp,
    primary key (id),
    unique key uk_user_id (user_id),
    unique key uk_address (address)
) engine=InnoDB default charset=utf8mb4;

create table if not exists tb_project
(
    id int not null auto_increment,
    project_id bigint not null,
    name varchar(255) not null,
    creator_id bigint not null,
    tags text,
    theme int not null default 0,
    project_description text,
    website_url varchar(255) not null default '',
    telegram_url varchar(255) not null default '',
    banner varchar(255) not null default '',
    empty_campaign_text varchar(255) not null default '',
    created_at datetime not null default current_timestamp,
    updated_at datetime not null default current_timestamp on update current_timestamp,
    primary key (id),
    unique key uk_project_id (project_id),
    index idx_creator (creator_id)
    ) engine=InnoDB default charset=utf8mb4;

create table if not exists tb_nft
(
    id int not null auto_increment,
    nft_id bigint not null,
    project_id bigint not null,
    creator bigint not null,
    name varchar(255) not null,
    contract varchar(255) not null,
    chain_id int not null,
    cover_url varchar(255) not null default '',
    pic_url varchar(255) not null default '',
    mint_cap bigint not null,
    unlimited varchar(255) not null default '',
    created_at datetime not null default current_timestamp,
    updated_at datetime not null default current_timestamp on update current_timestamp,
    primary key (id),
    unique key uk_nft_id (nft_id),
    unique key uk_address (contract, chain_id),
    index idx_project_id (project_id)
    ) engine=InnoDB default charset=utf8mb4;

CREATE TABLE IF NOT EXISTS tb_token
(
    id INT NOT NULL AUTO_INCREMENT,
    token_id BIGINT NOT NULL,
    name VARCHAR(128) not null default '',
    net_work VARCHAR(255) not null default '',
    number BIGINT not null default 0,
    contract VARCHAR(255) not null default '',
    chain_id INT not null default 0,
    method_type INT not null default 0,
    reward_num BIGINT not null default 0,
    project_id BIGINT not null,
    creator_id BIGINT not null,
    group_id BIGINT not null,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY uk_token_id_group_id (token_id, group_id),
    KEY idx_project_id (project_id),
    KEY idx_group (group_id)
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `tb_sbt_reward` (
    `id` int NOT NULL AUTO_INCREMENT,
    `sbt_id` bigint NOT NULL,
    `name` varchar(255) NOT NULL DEFAULT '',
    `activity_id` int NOT NULL,
    `pic_url` varchar(255) NOT NULL DEFAULT '',
    `number` bigint NOT NULL,
    `method_type` int NOT NULL,
    `unlimited` varchar(255) NOT NULL,
    `reward_num` bigint NOT NULL,
    `project_id` bigint NOT NULL,
    `creator_id` bigint NOT NULL,
    `group_id` bigint NOT NULL,
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_sbt_id` (`sbt_id`),
    KEY `idx_project_id` (`project_id`),
    KEY `idx_group` (`group_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `tb_nft_group` (
     `id` int NOT NULL AUTO_INCREMENT,
     `nft_id` bigint NOT NULL,
     `project_id` bigint NOT NULL,
     `creator_id` bigint NOT NULL,
     `group_id` bigint NOT NULL,
     `method_type` int NOT NULL,
     `name` varchar(255) NOT NULL,
     `symbol` varchar(255) NOT NULL DEFAULT '',
     `contract` varchar(255) NOT NULL,
     `chain_id` int NOT NULL,
     `cover_url` varchar(255) NOT NULL DEFAULT '',
     `pic_url` varchar(255) NOT NULL DEFAULT '',
     `mint_cap` bigint NOT NULL,
     `unlimited` varchar(255) NOT NULL DEFAULT '',
     `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
     `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
     PRIMARY KEY (`id`),
     UNIQUE KEY `uk_nft_group_id` (`nft_id`,`group_id`),
     KEY `idx_project_id` (`project_id`)
) ENGINE=InnoDB AUTO_INCREMENT=156 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

create table if not exists tb_credential
(
    id int not null auto_increment,
    credential_id bigint not null,
    name varchar(255) not null,
    name_exp varchar(255) not null,
    label_type int not null,
    link varchar(255) not null,
    project_id bigint not null,
    creator_id bigint not null,
    created_at datetime not null default current_timestamp,
    updated_at datetime not null default current_timestamp on update current_timestamp,
    primary key (id),
    unique key uk_credential_id (credential_id),
    index idx_project_id (project_id)
) engine=InnoDB default charset=utf8mb4;

create table if not exists tb_credential_group
(
    id int not null auto_increment,
    group_id bigint not null,
    name varchar(255) not null,
    group_type int not null ,
    project_id bigint not null,
    creator_id bigint not null,
    campaign_id bigint not null,
    status int not null,
    created_at datetime not null default current_timestamp,
    updated_at datetime not null default current_timestamp on update current_timestamp,
    primary key (id),
    unique key uk_group_id (group_id),
    index idx_project_id (project_id)
) engine=InnoDB default charset=utf8mb4;

create table if not exists tb_point
(
    id int not null auto_increment,
    point_id bigint not null,
    number bigint not null,
    method_type int not null,
    unlimited varchar(255) not null,
    reward_num bigint not null,
    project_id bigint not null,
    creator_id bigint not null,
    group_id bigint not null,
    claimed_num bigint not null default 0,
    created_at datetime not null default current_timestamp,
    updated_at datetime not null default current_timestamp on update current_timestamp,
    primary key (id),
    unique key uk_point_id (point_id),
    index idx_project_id (project_id)
) engine=InnoDB default charset=utf8mb4;

create table if not exists user_credential (
                                               id int not null auto_increment,
                                               user_id int not null,
                                               credential_id int not null,
                                               created_at datetime not null default current_timestamp,
                                               updated_at datetime not null default current_timestamp on update current_timestamp,
                                               primary key (id),
    unique key uk_user_credential (user_id, credential_id),
    index idx_credential_id (credential_id)
    ) engine=InnoDB default charset=utf8mb4;

create table if not exist action(
                                    id int not null auto_increment,
                                    action_id int not null,
                                    action_type varchar(32) not null, -- twitter follow, twitter retweet, twitter like, twitter comment, twitter mention, discord join, discord message, discord mention, discord reaction, discord reaction detail, on chain action
    action_detail text,
    status int not null default 0, -- 0: not effective, 1: effective
    created_at datetime not null default current_timestamp,
    updated_at datetime not null default current_timestamp on update current_timestamp,
    primary key (id),
    unique key uk_action_id (action_id),
    index key action_type (action_type)
    ) engine=InnoDB default charset=utf8mb4;

create table if not exists campaign
(
    id int not null auto_increment,
    campaign_id int not null,
    name varchar(255) not null,
    pic_url varchar(255) not null,
    description text,
    start_at datetime not null,
    end_at datetime not null,
    status int not null default 0, -- 0: not started, 1: started, 2: ended
    reward text, -- json string
    reward_action varchar(255) not null, -- json string
    project_id int not null,
    creator_id int not null,
    created_at datetime not null default current_timestamp,
    updated_at datetime not null default current_timestamp on update current_timestamp,
    primary key (id),
    unique key campaign_id (campaign_id),
    index project_id (project_id)
) engine=InnoDB default charset=utf8mb4;

create table tb_project_token(
    id int not null auto_increment,
    token_id bigint not null,
    token varchar(256) not null,
    status int not null default 1,
    project_id bigint not null,
    creator_id bigint not null,
    created_at datetime not null default current_timestamp,
    updated_at datetime not null default current_timestamp on update current_timestamp,
    primary key (id),
    unique key uk_token_id (token_id),
    index project_id (project_id)
) engine=InnoDB default charset=utf8mb4;

create table tb_user_campaign(
    id int not null auto_increment,
    user_campaign_id bigint not null,
    project_id bigint not null,
    campaign_id bigint not null,
    address varchar(255) not null,
    op_token varchar(255) not null,
    status int not null default 1,
    created_at datetime not null default current_timestamp,
    updated_at datetime not null default current_timestamp on update current_timestamp,
    primary key (id),
    unique key uk_user_campaign_id (user_campaign_id),
    index idx_project (project_id),
    index idx_campaign(campaign_id)
) engine=InnoDB default charset=utf8mb4;

create table tb_user_credential(
    id int not null auto_increment,
    user_credential_id bigint not null,
    address varchar(255) not null,
    credential_id bigint not null,
    op_token varchar(255) not null,
    status int not null default 1,
    created_at datetime not null default current_timestamp,
    updated_at datetime not null default current_timestamp on update current_timestamp,
    primary key (id),
    unique key uk_uc_id (user_credential_id),
    index idx_address (address),
    index idx_credential(credential_id)
) engine=InnoDB default charset=utf8mb4;

create table tb_user_credential_new(
    id int not null auto_increment,
    user_id bigint not null,
    address varchar(255) not null,
    credential_id bigint not null,
    campaign_id bigint not null,
    status int not null default 1,
    participant_date datetime not null default current_timestamp,
    created_at datetime not null default current_timestamp,
    updated_at datetime not null default current_timestamp on update current_timestamp,
    primary key (id),
    index idx_address (address),
    index idx_credential(credential_id)
) engine=InnoDB default charset=utf8mb4;

create table tb_user_campaign_new(
    id int not null auto_increment,
    user_id bigint not null,
    address varchar(255) not null,
    campaign_id bigint not null,
    point_num bigint not null ,
    status int not null default 1,
    created_at datetime not null default current_timestamp,
    updated_at datetime not null default current_timestamp on update current_timestamp,
    primary key (id),
    index idx_address (address),
    index idx_campaign(campaign_id)
) engine=InnoDB default charset=utf8mb4;

create table tb_user_group_new(
    id int not null auto_increment,
    user_id bigint not null,
    address varchar(255) not null,
    group_id bigint not null,
    point_num bigint not null ,
    status int not null default 1,
    created_at datetime not null default current_timestamp,
    updated_at datetime not null default current_timestamp on update current_timestamp,
    primary key (id),
    index idx_address (address),
    index idx_group(group_id)
) engine=InnoDB default charset=utf8mb4;

create table tb_campaign_credential(
    id int not null auto_increment,
    campaign_credential_id bigint not null,
    campaign_id bigint not null,
    credential_id bigint not null,
    status int not null default 0,
    created_at datetime not null default current_timestamp,
    updated_at datetime not null default current_timestamp on update current_timestamp,
    primary key (id),
    unique key uk_cc_id (campaign_credential_id),
    unique key uk_campaign_credential (campaign_id, credential_id)
) engine=InnoDB default charset=utf8mb4;

 -- update
 --    alter table tb_credential add column group_id bigint not null after creator_id;
 --    alter table tb_nft add column group_id bigint not null after creator_id;
 --    alter table  tb_nft add column method_type int not null after groupId;
 --    alter table tb_credential add column name_exp varchar(255) not null after name;
 --    alter table tb_point add column unlimited varchar(255) not null after method_type;
 --    alter table tb_credential add column label_type int not null after name_exp;
 --    alter table tb_credential add column link varchar(255) not null after label_type;
 --    alter table tb_credential add column group_type int not null after group_id;
 --    alter table tb_credential add column space_id varchar(255) not null after group_type;
 --    alter table tb_credential add column campaign_id bigint not null after space_id;
 --    alter table tb_credential add column is_verified int not null after campaign_id;
 --    alter table tb_credential add column role_id bigint not null after is_verified;
 --    alter table tb_credential add column role_name varchar(128) not null after role_id;
 --    alter table tb_credential add column page_name varchar(128) not null after role_name;
 --    alter table tb_nft add column pic_url varchar(255) not null default '' after cover_url;
 --    alter table tb_nft add column mint_cap bigint not null after pic_url;
 --    alter table tb_nft add column unlimited varchar(255) not null default '' after mint_cap;
 --    alter table tb_user add column ar_address varchar(255) not null default '' after wallet_address;
 --    alter table tb_credential add column proposal_id varchar(255) not null after page_name;
--     alter table tb_user_credential_new add column is_twitter_login varchar(64) not null default 'false' after participant_date;
 --    alter table tb_project add column banner varchar(255) not null default '' after telegram_url;
--     alter table tb_project add column campaign_text varchar(255) not null default '' after banner;
 --    alter table tb_project add column company_id int not null default 0 after project_id;
 --    alter table tb_project add column project_custom_item_a varchar(255) not null default '' after empty_campaign_text;
 --    alter table tb_user_credential_new add column amount2 bigint not null default 0 after amount;
 --    alter table tb_user_credential_new add column total_amount bigint not null default 0 after amount2;
 --    alter table tb_project add column layer_one_list text not null default '' after project_custom_item_a;
 --    alter table tb_project add column discord_link varchar(255) not null default '' after telegram_link;
--     alter table tb_user_credential_new add column amount3 bigint not null default 0 after amount2;
 --    alter table tb_wise_invite_record add column event_stage int not null default 1 after invite_code;



create table tb_user_twitter(
    id int not null auto_increment,
    user_id bigint not null,
    state varchar(255) not null default '',
    code_challenge varchar(255) not null default '',
    access_token varchar(255) not null default '',
    refresh_token varchar(255) not null default '',
    twitter_id varchar(255) not null default '',
    twitter_name varchar(255) not null default '',
    twitter_screen_name varchar(255) not null default '',
    connected int not null default 0,
    status int not null default 0,
    created_at datetime not null default current_timestamp,
    updated_at datetime not null default current_timestamp on update current_timestamp,
    primary key (id),
    unique key uk_user_id (user_id)
) engine=InnoDB default charset=utf8mb4;

alter table tb_user_twitter add unique index uk_twitter_id (twitter_id);

create table if not exists tb_tw_state_challenge(
    id int not null auto_increment,
    state varchar(128) not null default '',
    challenge varchar(128) not null default '',
    created_at datetime not null default current_timestamp,
    updated_at datetime not null default current_timestamp on update current_timestamp,
    primary key (id),
    index idx_state (state)
) engine=InnoDB default charset=utf8mb4;

CREATE TABLE tb_participant
(
    id int not null auto_increment,
    user_id BIGINT NOT NULL,
    campaign_id BIGINT NOT NULL,
    wallet_address VARCHAR(128) NOT NULL,
    point_num BIGINT NOT NULL,
    is_join VARCHAR(128),
    is_visit VARCHAR(128),
    credentials text,
    verified_credentials text,
    participant_date DATETIME NOT NULL,
    created_at datetime not null default current_timestamp,
    updated_at datetime not null default current_timestamp on update current_timestamp,
    primary key (id)
) engine=InnoDB default charset=utf8mb4;
alter table tb_project add unique index uk_url (project_url);

create table tb_twitter_name_id_mapping(
    id int not null auto_increment,
    twitter_username varchar(255) not null,
    twitter_id varchar(255) not null,
    created_at datetime not null default current_timestamp,
    updated_at datetime not null default current_timestamp on update current_timestamp,
    primary key (id),
    unique key uk_twitter_username (twitter_username),
    unique key uk_twitter_id (twitter_id)
) engine=InnoDB default charset=utf8mb4;

create table tb_space_info(
    id int not null auto_increment,
    space_id varchar(255) not null,
    space_name varchar(255) not null,
    creator_id varchar(255) not null,
    creator_name varchar(255) not null,
    description text,
    raw_data text,
    start_at datetime not null,
    created_at datetime not null default current_timestamp,
    updated_at datetime not null default current_timestamp on update current_timestamp,
    primary key (id),
    unique key uk_space_id (space_id)
) engine=InnoDB default charset=utf8mb4;

create table tb_user_telegram(
    id int not null auto_increment,
    user_id bigint not null,
    tg_id bigint not null,
    first_name varchar(255) not null default '',
    last_name varchar(255) not null default '',
    username varchar(255) not null default '',
    photo_url varchar(255) not null default '',
    auth_date datetime not null,
    connected int not null default 0,
    created_at datetime not null default current_timestamp,
    updated_at datetime not null default current_timestamp on update current_timestamp,
    primary key (id),
    unique key uk_user_id (user_id),
    unique key tg_id (tg_id)
) engine=InnoDB default charset=utf8mb4;

create table tb_user_discord(
    id int not null auto_increment,
    user_id bigint not null,
    dc_id varchar(255) not null,
    username varchar(255) not null default '',
    avatar varchar(255) not null default '',
    global_name varchar(255) not null default '',
    access_token varchar(255) not null default '',
    refresh_token varchar(255) not null default '',
    token_expire bigint not null default 0,
    connected int not null default 0,
    created_at datetime not null default current_timestamp,
    updated_at datetime not null default current_timestamp on update current_timestamp,
    primary key (id),
    unique key uk_user_id (user_id),
    unique key uk_dc_id (dc_id)
) engine=InnoDB default charset=utf8mb4;

create table tb_credential_sign(
    id int not null auto_increment,
    user_id bigint not null,
    credential_id bigint not null,
    raw_data varchar(255) not null,
    sign varchar(255) not null default '',
    verified int not null default 0,
    sign_time datetime null,
    created_at datetime not null default current_timestamp,
    updated_at datetime not null default current_timestamp on update current_timestamp,
    primary key (id),
    unique key uk_user_id (user_id, credential_id)
) engine=InnoDB default charset=utf8mb4;

create table tb_credential_airdrop_address(
    id int not null auto_increment,
    user_id bigint not null,
    credential_id bigint not null,
    wallet_address VARCHAR(128) NOT NULL,
    verified int not null default 0,
    created_at datetime not null default current_timestamp,
    updated_at datetime not null default current_timestamp on update current_timestamp,
    primary key (id),
    unique key uk_user_id (user_id, credential_id)
) engine=InnoDB default charset=utf8mb4;

create table tb_user_reward(
    id int not null auto_increment,
    reward_id bigint not null,
    reward_type int not null,
    user_id bigint not null,
    group_id bigint not null,
    claim_type int not null,
    created_at datetime not null default current_timestamp,
    updated_at datetime not null default current_timestamp on update current_timestamp,
    primary key (id),
    unique key uk_user_id (user_id, reward_id)
) engine=InnoDB default charset=utf8mb4;

create table tb_user_nft(
    id int not null auto_increment,
    user_id bigint not null,
    nft_id bigint not null,
    dummy_id varchar(255) not null,
    signature varchar(255) not null,
    tx varchar(255) not null,
    created_at datetime not null default current_timestamp,
    updated_at datetime not null default current_timestamp on update current_timestamp,
    primary key (id),
    index idx_user_id (user_id),
    index idx_nft_id (nft_id),
    unique key uk_dummy_id (dummy_id)
) engine=InnoDB default charset=utf8mb4;

create table tb_project_external_config(
    id int not null auto_increment,
    project_id bigint not null,
    app_key varchar(255) not null,
    callback_url varchar(255) not null,
    status int not null default 0,
    extra text,
    created_at datetime not null default current_timestamp,
    updated_at datetime not null default current_timestamp on update current_timestamp,
    primary key (id),
    unique key uk_project_id (project_id)
) engine=InnoDB default charset=utf8mb4;

create table tb_callback_history(
    id int not null auto_increment,
    project_id bigint not null,
    credential_id bigint not null,
    user_id bigint not null,
    callback_url varchar(255) not null,
    status varchar(128) not null default 'INIT',
    times int not null default 0,
    content text,
    created_at datetime not null default current_timestamp,
    updated_at datetime not null default current_timestamp on update current_timestamp,
    primary key (id),
    unique key uk_c_u_id (credential_id, user_id),
    index idx_project_id (project_id)
) engine=InnoDB default charset=utf8mb4;

create table tb_dummy_id(
    id bigint not null auto_increment,
    user_id bigint not null,
    credential_group_id bigint not null,
    created_at datetime not null default current_timestamp,
    updated_at datetime not null default current_timestamp on update current_timestamp,
    primary key (id),
    unique key uk_user_id (user_id, credential_group_id)
) engine=InnoDB default charset=utf8mb4;

create index idx_campaign
    on tb_credential_group (campaign_id);
create index idx_group
    on tb_credential (group_id);
create index idx_group
    on tb_nft_group (group_id);
create index idx_group
    on tb_point (group_id);
create index idx_user
    on tb_user_credential_new (user_id);

create table tb_addr_nonce(
    id bigint not null auto_increment,
    address varchar(255) not null,
    nonce varchar(255) not null,
    status int not null default 1, -- 1: valid, 0: invalid
    created_at datetime not null default current_timestamp,
    updated_at datetime not null default current_timestamp on update current_timestamp,
    primary key (id),
    unique key address (address)
) engine=InnoDB default charset=utf8mb4;

create table tb_zk_login(
    id bigint not null auto_increment,
    user_id bigint not null,
    issuer varchar(255) not null,
    sub varchar(255) not null,
    identity varchar(255) not null,
    salt varchar(255) not null,
    address varchar(255) not null,
    created_at datetime not null default current_timestamp,
    updated_at datetime not null default current_timestamp on update current_timestamp,
    primary key (id),
    unique key uk_user_id (user_id),
    unique key uk_iss_id (identity, issuer),
    unique key uk_sub (issuer, sub)
) engine = InnoDB default charset=utf8mb4;

alter table tb_user_twitter add column twitter_profile_image varchar(255) not null default '' after twitter_screen_name;

alter table tb_user add column new_user int not null default 0 after avatar;

create table tb_admin_nonce_sign(
    id bigint not null auto_increment,
    user_id bigint not null,
    project_id bigint not null,
    address varchar(255) not null,
    nonce varchar(255) not null,
    op varchar(16) not null,
    sign varchar(255) not null default '',
    status int not null default 1, -- 1: valid, 0: invalid
    created_at datetime not null default current_timestamp,
    updated_at datetime not null default current_timestamp on update current_timestamp,
    primary key (id),
    index idx_uid(user_id),
    index idx_project_id(project_id)
) engine=InnoDB default charset=utf8mb4;

CREATE TABLE tb_unbind (
    id BigInt NOT NULL AUTO_INCREMENT,
    user_id BigInt NOT NULL,
    address varchar(255) NOT NULL,
    social_type int NOT NULL,
    social_id varchar(128) NOT NULL,
    credential_id BigInt NOT NULL,
    created_at datetime NOT NULL default current_timestamp,
    updated_at datetime NOT NULL default current_timestamp on update current_timestamp,
    PRIMARY KEY (id),
    UNIQUE KEY `comp_key` (user_id,social_id,credential_id),
    INDEX `user_id_index` (`user_id`),
    INDEX `social_id_index` (`social_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;

ALTER TABLE tb_user_credential_new
    ADD COLUMN social_id varchar(128) NOT NULL DEFAULT '',
    ADD COLUMN social_type int NOT NULL DEFAULT 0,
    ADD COLUMN label_type int NOT NULL DEFAULT 0;

create table if not exists tb_user_ton(
    id int not null auto_increment,
    user_id bigint not null,
    address varchar(128) not null,
    public_key varchar(256) not null,
    created_at datetime not null default current_timestamp,
    updated_at datetime not null default current_timestamp on update current_timestamp,
    primary key (id),
    unique key uk_user_id (user_id),
    unique key uk_address (address)
) engine=InnoDB default charset=utf8mb4;

CREATE TABLE if not exists `tb_user_wise_score` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `user_id` bigint(20) NOT NULL,
    `address` varchar(128) NOT NULL DEFAULT '',
    `address_type` int NOT NULL DEFAULT 0,
    `avatar` varchar(256) NOT NULL DEFAULT '',
    `total_score` int NOT NULL DEFAULT 0,
    `wealth_score` int NOT NULL DEFAULT 0,
    `identity_score` int NOT NULL DEFAULT 0,
    `social_score` int NOT NULL DEFAULT 0,
    `engagement_score` int NOT NULL DEFAULT 0,
    `is_not_coin_holder` int NOT NULL DEFAULT 0,
    `has_not_coin_transaction` int NOT NULL DEFAULT 0,
    `rank` int NOT NULL DEFAULT 0,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `user_id` (`user_id`),
    INDEX `idx_total_score` (`total_score`),
    INDEX `idx_wealth_score` (`wealth_score`),
    INDEX `idx_identity_score` (`identity_score`),
    INDEX `idx_social_score` (`social_score`),
    INDEX `idx_engagement_score` (`engagement_score`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `tb_user_sui_wise_score` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
    `user_id` bigint NOT NULL,
    `address` varchar(128) NOT NULL DEFAULT '',
    `address_type` int NOT NULL DEFAULT '0',
    `avatar` varchar(256) NOT NULL DEFAULT '',
    `total_score` int NOT NULL DEFAULT '0',
    `wealth_score` int NOT NULL DEFAULT '0',
    `identity_score` int NOT NULL DEFAULT '0',
    `social_score` int NOT NULL DEFAULT '0',
    `engagement_score` int NOT NULL DEFAULT '0',
    `is_not_coin_holder` int NOT NULL DEFAULT '0',
    `has_not_coin_transaction` int NOT NULL DEFAULT '0',
    `has_ton_stake` int NOT NULL DEFAULT '0',
    `has_ton_liquidity_provide` int NOT NULL DEFAULT '0',
    `rank` int NOT NULL DEFAULT '0',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `user_id` (`user_id`),
    KEY `idx_total_score` (`total_score`),
    KEY `idx_wealth_score` (`wealth_score`),
    KEY `idx_identity_score` (`identity_score`),
    KEY `idx_social_score` (`social_score`),
    KEY `idx_engagement_score` (`engagement_score`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE if not exists `tb_user_sbt_list` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `user_id` bigint(20) NOT NULL,
    `address` varchar(128) NOT NULL DEFAULT '',
    `address_type` int NOT NULL DEFAULT 0,
    `avatar` varchar(256) NOT NULL DEFAULT '',
    `sbt_link` varchar(256) NOT NULL DEFAULT '',
    `claimed_type` int NOT NULL DEFAULT 0,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE if not exists `tb_user_lucky_draw` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `user_id` bigint(20) NOT NULL,
    `fission_level` int NOT NULL DEFAULT 0,
    `t_points` int NOT NULL DEFAULT 0,
    `is_wise_score` int NOT NULL DEFAULT 0,
    `is_sbt` int NOT NULL DEFAULT 0,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    INDEX `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE if not exists `tb_t_points_consume` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `user_id` bigint(20) NOT NULL,
    `consume_type` int NOT NULL DEFAULT 0,
    `level` int NOT NULL DEFAULT 0,
    `t_points` int NOT NULL DEFAULT 0,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    INDEX `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE if not exists `tb_t_points_vanguard` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `user_id` bigint(20) NOT NULL,
    `level` int NOT NULL DEFAULT 0,
    `t_points` int NOT NULL DEFAULT 0,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    INDEX `idx_user_id` (`user_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


CREATE TABLE `tb_user_share_link` (
    `id` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `user_id` BIGINT(20) NOT NULL DEFAULT 0,
    `social_type` INT(11) NOT NULL  DEFAULT 0,
    `share_link` VARCHAR(128) NOT NULL  DEFAULT '',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_index` (`user_id`, `social_type`, `share_link`),
    INDEX `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `tb_token_air_drop` (
    `id` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `token_name` VARCHAR(128) NOT NULL DEFAULT '',
    `address` VARCHAR(128) NOT NULL DEFAULT '',
    `user_id` BIGINT(20) NOT NULL DEFAULT 0,
    `amount` VARCHAR(128) NOT NULL DEFAULT '',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_address` (`address`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `tb_project_apply` (
    `id` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `user_id` BIGINT NOT NULL,
    `project_name` VARCHAR(128) NOT NULL DEFAULT '',
    `category` VARCHAR(128) NOT NULL DEFAULT '',
    `tma_link` VARCHAR(128) NOT NULL DEFAULT '',
    `email` VARCHAR(128) NOT NULL DEFAULT '',
    `apply_reason` TEXT NOT NULL,
    `estimated_participants` INT(11) NOT NULL DEFAULT 0,
    `social_platforms` VARCHAR(128) NOT NULL,
    `additional_social_info` TEXT NOT NULL,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    INDEX `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `tb_user_project_privilege` (
    `id` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `user_id` BIGINT(20) NOT NULL,
    `status` INT(11) NOT NULL DEFAULT 0,
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_id` (`user_id`),
    INDEX `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `tb_telegram_invitation` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `user_id` BIGINT NOT NULL,
    `inviter_tg_id` BIGINT NOT NULL,
    `invitee_tg_id` BIGINT NOT NULL,
    `invitee_first_name` VARCHAR(128) NOT NULL DEFAULT '',
    `invitee_last_name` VARCHAR(128) NOT NULL DEFAULT '',
    `invitee_username` VARCHAR(128) NOT NULL DEFAULT '',
    `invite_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    INDEX `idx_user` (`user_id`),
    INDEX `idx_inviter` (`inviter_tg_id`),
    UNIQUE INDEX `uk_invitee` (`invitee_tg_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `tb_tg_lucky_draw_times` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `user_id` BIGINT NOT NULL,
    `tg_user_id` BIGINT NOT NULL,
    `times` INT NOT NULL DEFAULT 0,
    `last_daily_add_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `last_add_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `tb_daily_lucky_draw_times`
(
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `user_id` BIGINT NOT NULL,
    `times` INT NOT NULL DEFAULT 0,
    `used_times` INT NOT NULL DEFAULT 0,
    `free_times` INT NOT NULL DEFAULT 0,
    `bonus_times` INT NOT NULL DEFAULT 0,
    `date` VARCHAR(64) NOT NULL,
    `next_add_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `last_use_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE INDEX `uk_user_date` (user_id, `date`),
    INDEX `idx_date`(date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

create table `tb_tg_private_group_info`
(
    `id` bigint not null auto_increment,
    `tg_id` bigint not null,
    `private_hash` varchar(128) not null,
    `status` varchar(128) not null,
    `operation_at` bigint not null,
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE INDEX `uk_tg_id` (`tg_id`),
    UNIQUE INDEX `uk_private_hash` (`private_hash`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

alter table tb_telegram_invitation add column is_premium int not null default 0 after invitee_username;

create table `tb_wise_score_one_time_task`
(
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `user_id` BIGINT NOT NULL,
    `task_name` VARCHAR(128) NOT NULL,
    `task_time` TIMESTAMP NOT NULL,
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE INDEX `uk_user_task` (`user_id`, `task_name`),
    INDEX `idx_task` (`task_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

create table  `tb_wise_score_invite`
(
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `user_id` BIGINT NOT NULL,
    `invite_code` VARCHAR(32) NOT NULL,
    `total_times` INT NOT NULL DEFAULT 3,
    `used_times` INT NOT NULL DEFAULT 0,
    `status` INT NOT NULL DEFAULT 1,
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    INDEX `idx_user` (`user_id`),
    UNIQUE INDEX `uk_invite_code` (`invite_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

create table  `tb_wise_invite_record`
(
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `user_id` BIGINT NOT NULL,
    `invite_tg_name` VARCHAR(128) NOT NULL,
    `invite_code` VARCHAR(32) NOT NULL,
    `invitee_id` BIGINT NOT NULL,
    `invite_at` TIMESTAMP NOT NULL,
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    INDEX `idx_user` (`user_id`),
    UNIQUE INDEX `uk_invitee` (`invitee_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `tb_coin_and_token_price` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    `token_name` VARCHAR(32) NOT NULL,
    `price` DOUBLE NOT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE tb_ton_society_sync_privilege(
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `project_id` BIGINT NOT NULL,
    `campaign_id` BIGINT NOT NULL,
    `status` INT NOT NULL DEFAULT 1,
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    INDEX `idx_project_id` (`project_id`),
    INDEX `idx_campaign_id` (`campaign_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE tb_ton_sync_history(
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `project_id` BIGINT NOT NULL,
    `campaign_id` BIGINT NOT NULL,
    `title` VARCHAR(255) NOT NULL,
    `subtitle` VARCHAR(255) NOT NULL,
    `description` TEXT NOT NULL,
    `start_date` TIMESTAMP NOT NULL,
    `end_date` TIMESTAMP NOT NULL,
    `button_link` VARCHAR(255) NOT NULL,
    `sbt_collection_title` VARCHAR(255) NOT NULL,
    `sbt_collection_desc` VARCHAR(255) NOT NULL,
    `sbt_image` VARCHAR(255) NOT NULL,
    `sbt_desc` VARCHAR(255) NOT NULL,
    `sync_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `activity_id` BIGINT NOT NULL DEFAULT 0,
    `activity_url` VARCHAR(255) NOT NULL DEFAULT '',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    INDEX `idx_project_id` (`project_id`),
    INDEX `idx_campaign_id` (`campaign_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS tb_company (
    id INT NOT NULL AUTO_INCREMENT,
    company_id INT NOT NULL DEFAULT 0,
    company_name VARCHAR(255) NOT NULL,
    creator_id BIGINT NOT NULL,
    company_description TEXT,
    logo VARCHAR(128) DEFAULT '',
    channel_name VARCHAR(128) DEFAULT '',
    channel_link VARCHAR(128) DEFAULT '',
    twitter_link VARCHAR(128) DEFAULT '',
    web_url VARCHAR(128) DEFAULT '',
    about_bg_image VARCHAR(128) DEFAULT '',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY uk_company_id (company_id),
    INDEX idx_creator (creator_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS tb_layer_one (
    id INT NOT NULL AUTO_INCREMENT,
    layer_one_id INT NOT NULL DEFAULT 0,
    name VARCHAR(255) NOT NULL,
    creator_id BIGINT NOT NULL,
    icon VARCHAR(128) DEFAULT '',
    url VARCHAR(128) DEFAULT '',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY uk_layer_one_id (layer_one_id),
    INDEX idx_creator (creator_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS tb_company_layer_one_relation (
    id INT NOT NULL AUTO_INCREMENT,
    company_id INT NOT NULL,
    layer_one_id INT NOT NULL,
    status TINYINT NOT NULL DEFAULT 0,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY uk_company_layer_one (company_id, layer_one_id),
    INDEX idx_company_id (company_id),
    INDEX idx_layer_one_id (layer_one_id),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS tb_company_config (
    id INT NOT NULL AUTO_INCREMENT,
    company_id INT NOT NULL DEFAULT 0,
    config_key VARCHAR(255) NOT NULL,
    config_value VARCHAR(255) NOT NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY uk_company_config (company_id, config_key),
    INDEX idx_company_id (company_id),
    INDEX idx_config_key (config_key)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

create table if not exists tb_latenight_airdrop_user
(
    id int not null auto_increment,
    user_id bigint not null default 0,
    ton_address varchar(255) not null default '',
    wallet_age int not null default 0,
    ton_balance bigint not null default 0,
    transaction_num int not null default 0,
    created_at datetime not null default current_timestamp,
    updated_at datetime not null default current_timestamp on update current_timestamp,
    primary key (id),
    unique key uk_user_id (user_id)
    ) engine=InnoDB default charset=utf8mb4;

CREATE TABLE tb_home_project_template (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    template_name VARCHAR(128) NOT NULL DEFAULT '' UNIQUE,
    banner_title VARCHAR(255) NOT NULL DEFAULT '',
    banner_pic_url VARCHAR(255) NOT NULL DEFAULT '',
    banner_sbt_ids TEXT NOT NULL DEFAULT '',
    banner_sub_title VARCHAR(255) NOT NULL DEFAULT '',
    middle_title VARCHAR(255) NOT NULL DEFAULT '',
    middle_description VARCHAR(255) NOT NULL DEFAULT '',
    ton_hero_campaign_id BIGINT NOT NULL DEFAULT 0,
    ton_hero_sbt_pic_url VARCHAR(255) NOT NULL DEFAULT '',
    ton_hero_title VARCHAR(255) NOT NULL DEFAULT '',
    ton_hero_description VARCHAR(255) NOT NULL DEFAULT '',
    campaign_ids TEXT NOT NULL DEFAULT '',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

alter table tb_home_project_template add column ton_hero_campaign_id BIGINT NOT NULL DEFAULT 0 after middle_description;
alter table tb_home_project_template add column ton_hero_sbt_pic_url VARCHAR(255) NOT NULL DEFAULT '' after ton_hero_campaign_id;
alter table tb_credential_group add column exist_activity_id int not null default 0 after status;

CREATE TABLE tb_tg_user_push (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    tg_push_setting_id bigint NOT NULL DEFAULT 0,
    tg_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    push_type VARCHAR(128) NOT NULL DEFAULT '',
    participate_type INT NOT NULL DEFAULT 0,
    group_id INT NOT NULL DEFAULT 0,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_tg_id (tg_id),
    INDEX idx_tg_push_setting_id (tg_push_setting_id)
);

alter table tb_wise_score_invite add column event_stage int not null after user_id;
alter table tb_sbt_reward add column category int not null default 0 after name;

CREATE TABLE tb_twitter_user_code (
    id BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL UNIQUE, -- userId 单独唯一
    user_code VARCHAR(32) NOT NULL UNIQUE, -- userCode 单独唯一
    status INT NOT NULL DEFAULT 0,
    tweet_id VARCHAR(128) NOT NULL DEFAULT '',
    twitter_id VARCHAR(128) NOT NULL DEFAULT '',
    twitter_name VARCHAR(255) NOT NULL DEFAULT '',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE tb_wise_score_user_check_in (  -- 用户打卡记录表
    id BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL, -- 用户 ID
    check_in_date TIMESTAMP NOT NULL, -- 打卡日期
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_user_check_in (user_id, check_in_date) -- 联合唯一索引
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE tb_user_streaks ( -- 用户wise score连续打卡表
    id BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL ,
    current_streak INT DEFAULT 0, -- 当前连续打卡天数，默认为 0
    max_streak INT DEFAULT 0, -- 历史最长连续打卡天数，默认为 0
    max_streak_begin_date TIMESTAMP, -- 历史最长连续打卡天数 - 起始日期
    max_streak_end_date TIMESTAMP, -- 历史最长连续打卡天数 - 结束日期
    last_check_in_date TIMESTAMP, -- 最后一次打卡的日期
    total_sign_cards_used INT DEFAULT 0, -- 累计使用的补签卡数量
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE tb_user_sign_card ( --用户wise score打卡补签表
    id BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY, -- 补签记录 ID
    user_id BIGINT NOT NULL, -- 用户 ID
    missed_date TIMESTAMP NOT NULL, -- 被补签的日期
    card_level INT NOT NULL, -- 补签卡的等级（对应不同的价格区间）
    card_price DOUBLE NOT NULL, -- 补签卡的价格（TON）
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 补签操作时间
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_user_id_missed_date` (`user_id`,`missed_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE tb_retroactive_card_inventory (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    inventory_id BIGINT NOT NULL, -- 库存 ID
    user_id BIGINT NOT NULL, -- 用户 ID
    total_cards INT NOT NULL, -- 总补签卡数量
    balance_cards INT NOT NULL, -- 剩余补签卡数量
    used_cards INT NOT NULL, -- 已使用补签卡数量
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_user_id (user_id),
    UNIQUE KEY uk_inventory_id (inventory_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE tb_retroactive_card_ledger (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    ledger_id BIGINT NOT NULL, -- 流水 ID
    user_id BIGINT NOT NULL, -- 用户 ID
    out_id VARCHAR(255) NOT NULL,
    spend_amount BIGINT NOT NULL DEFAULT 0, -- 消费金额
    added_amount BIGINT NOT NULL DEFAULT 0, -- 增加数量
    from_amount BIGINT NOT NULL DEFAULT 0, -- 原有补签卡数量
    to_amount BIGINT NOT NULL DEFAULT 0, -- 剩余补签卡数量
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_ledger_id (ledger_id),
    UNIQUE KEY uk_out_id (out_id),
    INDEX idx_user_id (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE tb_star_invoices (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    invoice_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    amount INT NOT NULL,
    product VARCHAR(255) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_invoice_id (invoice_id),
    INDEX idx_user_id (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE tb_start_payments (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    payment_id BIGINT NOT NULL,
    telegram_charge_id VARCHAR(255) NOT NULL,
    user_id BIGINT NOT NULL,
    invoice_id BIGINT NOT NULL,
    star_amount INT NOT NULL,
    product_amount INT NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_payment_id (payment_id),
    UNIQUE KEY uk_telegram_charge_id (telegram_charge_id),
    UNIQUE KEY uk_invoice_id (invoice_id),
    INDEX idx_user_id (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE tb_ai_agent
(
    id                  BIGINT AUTO_INCREMENT PRIMARY KEY,                                           -- 主键，自动递增
    agent_id            BIGINT       NOT NULL,                                                       -- ID
    agent_name          VARCHAR(255) NOT NULL,                                                       -- 名称
    agent_image         VARCHAR(512) NOT NULL,                                                       -- 图片 URL
    bio                 TEXT         NOT NULL,
    system_prompt       TEXT         NOT NULL,                                                       -- 系统提示
    lore                TEXT         NOT NULL,                                                       -- 背景知识
    status              INT          NOT NULL,                                                       -- 状态：0 - 默认，1 - 部署，2 - 停止

    -- intelligence
    knowledge_list      TEXT         NOT NULL,                                                       -- 知识点列表
    topics_list         TEXT         NOT NULL,                                                       -- 话题列表

    -- style setting
    adjectives          TEXT         NOT NULL,                                                       -- 描述性词语列表
    general_style_rules TEXT         NOT NULL,                                                       -- 通用风格规则

    -- model setting
    model_provider      VARCHAR(255) NOT NULL,                                                       -- 模型提供商
    model_name          VARCHAR(255) NOT NULL,                                                       -- 模型名称
    voice_model         VARCHAR(255) NOT NULL,                                                       -- 语音模型

    created_at          TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,                             -- 创建时间，默认为当前时间
    updated_at          TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, -- 更新时间，随更新自动更新

    UNIQUE KEY uk_agent_id (agent_id),                                                               -- 唯一键约束
    INDEX               idx_agent_name (agent_name)                                                  -- 索引，基于名称
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE tb_ai_tg_client_config
(
    id                    BIGINT AUTO_INCREMENT PRIMARY KEY,                                           -- 主键，自动递增
    tg_client_config_id   BIGINT       NOT NULL,                                                       -- Telegram 客户端配置 ID
    agent_id              BIGINT       NOT NULL,                                                       -- 关联代理 ID
    chat_styles           TEXT         NOT NULL,                                                       -- 聊天风格
    tg_bot_token          VARCHAR(255) NOT NULL,                                                       -- Telegram 机器人 Token
    conversation_examples TEXT         NOT NULL,                                                       -- 会话示例（存储为 JSON 格式）

    created_at            TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,                             -- 创建时间，默认为当前时间
    updated_at            TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, -- 更新时间，随记录更新自动更新

    UNIQUE KEY uk_tg_client_config_id (tg_client_config_id),                                           -- 唯一键约束
    INDEX                 idx_agent_id (agent_id)                                                      -- 索引，基于agent ID
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE tb_air_drop
(
    id                    BIGINT AUTO_INCREMENT PRIMARY KEY,                                           -- 主键，自动递增
    air_drop_id           BIGINT       NOT NULL,                                                       -- 空投 ID
    chain_id              INT          NOT NULL DEFAULT -1,
    chain_name            VARCHAR(255) NOT NULL,
    project_name          VARCHAR(255) NOT NULL,                                                       -- 项目名称
    project_desc          TEXT         NOT NULL,                                                       -- 项目描述
    social_link           VARCHAR(255) NOT NULL DEFAULT '',                                            -- 社交链接
    air_drop_desc         TEXT         NOT NULL DEFAULT '',                                            -- 空投描述
    token_name            VARCHAR(255) NOT NULL,                                                       -- 代币名称
    token_contract_address VARCHAR(255) NOT NULL DEFAULT '',                                           -- 合约地址
    airdrop_contract_address VARCHAR(255) NOT NULL DEFAULT '',                                           -- 合约地址
    status                INT          NOT NULL DEFAULT 0,                                             -- 状态：0 - 未开始, 1 - 进行中, 2 - 已结束
    start_time            TIMESTAMP    NULL,                                                           -- 开始时间
    end_time              TIMESTAMP    NULL,                                                           -- 结束时间
    created_at            TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,                             -- 创建时间，默认为当前时间
    updated_at            TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, -- 更新时间，随记录更新自动更新

    UNIQUE KEY uk_air_drop_id (air_drop_id),                                                           -- 唯一键约束
    INDEX                 idx_status (status)                                                          -- 索引，基于状态字段
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE tb_user_air_drop_info
(
    id                 BIGINT AUTO_INCREMENT PRIMARY KEY,                                           -- 主键，自动递增
    user_id            BIGINT       NOT NULL,                                                       -- 用户 ID
    chain              VARCHAR(255) NOT NULL DEFAULT '',                                            -- 链名称
    address            VARCHAR(255) NOT NULL DEFAULT '',                                            -- 钱包地址
    air_drop_id        BIGINT       NOT NULL,
    air_drop_token_cnt INT          NOT NULL DEFAULT 0,                                             -- 空投代币数量
    claimed_type       INT          NOT NULL DEFAULT 0,                                             -- 领取状态：0 - 未领取, 1 - 领取中, 2 - 已领取

    created_at         TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,                             -- 创建时间，默认为当前时间
    updated_at         TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, -- 更新时间，随记录更新自动更新

    UNIQUE KEY uk_user_address (user_id, address),                                                  -- 用户 ID 和地址的联合唯一键
    INDEX              idx_claimed_type (claimed_type),                                             -- 索引，基于领取状态
    INDEX              idx_user_id (user_id)                                                       -- 索引，基于用户 ID
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE tb_tg_user_push_setting
(
    id                  BIGINT AUTO_INCREMENT PRIMARY KEY,                                           -- 主键，自动递增
    tg_push_id          BIGINT       NOT NULL DEFAULT 0,                                             -- 推送 ID
    push_type           VARCHAR(255) NOT NULL DEFAULT '',                                            -- 推送类型
    user_type           INT          NOT NULL DEFAULT 0,                                             -- 用户类型：0 - ALL USER, 1 - SBT holder, etc.
    activity_id         INT          DEFAULT NULL,                                                   -- 活动 ID，当 userType = 1 时才有值
    top_wise_score_type INT          DEFAULT NULL,                                                   -- 顶级 Wise 分数类型
    wise_score_type     INT          DEFAULT NULL,                                                   -- Wise 分数条件类型
    wise_score_limit    INT          DEFAULT NULL,                                                   -- 分数限制
    has_groups          INT          DEFAULT NULL,                                                   -- 是否分组：0 - 不分组，1 - 分组
    has_pushed          INT          NOT NULL DEFAULT 0,                                             -- 推送状态：0 - 未推送，1 - 已推送

    created_at          TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,                             -- 创建时间，默认为当前时间
    updated_at          TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, -- 更新时间，随记录更新自动更新

    UNIQUE KEY uk_tg_push_id (tg_push_id),                                                           -- tg_push_id 的唯一键
    INDEX idx_user_type (user_type),                                                                 -- 索引，基于用户类型
    INDEX idx_has_pushed (has_pushed)                                                                -- 索引，基于推送状态
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE tb_push_basic_info
(
    id                  BIGINT AUTO_INCREMENT PRIMARY KEY,                                           -- 主键，自动递增
    tg_push_setting_id  BIGINT       NOT NULL,                                                       -- 外键，关联 tb_tg_user_push_setting 表
    img_url             VARCHAR(512) NOT NULL DEFAULT '',                                            -- 图片地址
    content             TEXT         NOT NULL,                                                      -- 推送内容
    button_name         VARCHAR(255) NOT NULL DEFAULT '',                                            -- 按钮名称
    button_url          VARCHAR(512) NOT NULL DEFAULT '',                                            -- 按钮链接地址
    push_date           TIMESTAMP    DEFAULT NULL,                                                   -- 推送时间

    created_at          TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,                             -- 创建时间，默认为当前时间
    updated_at          TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, -- 更���时间，随记录更新自动更新

    INDEX idx_tg_push_setting_id (tg_push_setting_id)                                               -- 索引，基于 tg_push_setting_id
--     CONSTRAINT fk_tg_push_setting_id FOREIGN KEY (tg_push_setting_id) REFERENCES tb_tg_user_push_setting (id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

ALTER TABLE tb_project ADD COLUMN chain VARCHAR(128) NOT NULL DEFAULT 'eth' AFTER layer_one_list;
alter table tb_project add column `goal` varchar(128) NOT NULL DEFAULT '' after chain;
alter table tb_project add column `tg_handle` varchar(128) NOT NULL DEFAULT '' after goal;
alter table tb_project add column `check_status` integer NOT NULL DEFAULT 0 after tg_handle;
alter table tb_ton_sync_check add column `task_category` varchar(128) NOT NULL DEFAULT '' after category;
alter table tb_ton_sync_check add column `network_id` integer NOT NULL DEFAULT 0 after task_category;
ALTER TABLE tb_user_reward
    ADD COLUMN sbt_object_id VARCHAR(255) NOT NULL DEFAULT '' AFTER reward_type,
ADD COLUMN tx VARCHAR(255) NOT NULL DEFAULT '' AFTER sbt_object_id;
alter table tb_project add column `project_email` varchar(128) NOT NULL DEFAULT '' after check_status;


create table tb_user_sui(
    id bigint not null auto_increment,
    user_id bigint not null,
    address varchar(255) not null,
    public_key varchar(255) not null default '',
    created_at datetime not null default current_timestamp,
    updated_at datetime not null default current_timestamp on update current_timestamp,
    primary key (id),
    unique key uk_user_id (user_id),
    unique key uk_address (address)
) engine=InnoDB default charset=utf8mb4;

CREATE TABLE `tb_sui_sbt_reward` (
    `id` BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY,
    `sbt_id` BIGINT NOT NULL,
    `activity_id` BIGINT NOT NULL default 0,
    `name` VARCHAR(255) NOT NULL,
    `desc` VARCHAR(255) NOT NULL,
    `url` VARCHAR(255) NOT NULL,
    `contract_address` VARCHAR(255) NOT NULL default '',
    `object_id` VARCHAR(255) NOT NULL default '',
    `project_id` BIGINT NOT NULL default 0,
    `creator_id` BIGINT NOT NULL default 0,
    `group_id` BIGINT NOT NULL default 0,
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX `idx_sbt_id` (`sbt_id`),
    INDEX `idx_activity_id` (`activity_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `tb_sui_sbt_sync` (
    `id` BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY,
    `activity_id` BIGINT NOT NULL default 0,
    `sbt_id` BIGINT NOT NULL default 0,
    `project_id` BIGINT NOT NULL default 0,
    `name` VARCHAR(255) NOT NULL,
    `desc` VARCHAR(255) NOT NULL,
    `url` VARCHAR(255) NOT NULL,
    `contract_address` VARCHAR(255) NOT NULL default '',
    `object_id` INT NOT NULL default 0,
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX `idx_activity_id` (`activity_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE tb_user_discord_voice_attendance (
                                                  id BIGINT PRIMARY KEY AUTO_INCREMENT,
                                                  user_id BIGINT NOT NULL,
                                                  dc_user_id VARCHAR(255) NOT NULL,
                                                  guild_id VARCHAR(255) NOT NULL,
                                                  channel_id VARCHAR(255) NOT NULL,
                                                  join_time TIMESTAMP NOT NULL,
                                                  leave_time TIMESTAMP NOT NULL,
                                                  duration BIGINT NOT NULL,
                                                  created_at timestamp not null default current_timestamp,
                                                  updated_at timestamp not null default current_timestamp on update current_timestamp,
                                                  index idx_user (user_id),
                                                  index idx_guild (guild_id),
                                                  index idx_channel (channel_id)
) engine=InnoDB default charset=utf8mb4;

