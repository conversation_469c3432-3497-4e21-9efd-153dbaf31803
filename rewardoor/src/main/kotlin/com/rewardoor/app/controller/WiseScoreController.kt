package com.rewardoor.app.controller

import com.rewardoor.app.auth.AddressAuthentication
import com.rewardoor.app.dao.SBTRewardRepository
import com.rewardoor.app.dao.TonSocietySyncRepository
import com.rewardoor.app.services.ProjectService
import com.rewardoor.app.services.TelegramService
import com.rewardoor.app.services.WiseInviteService
import com.rewardoor.app.services.WiseScoreService
import com.rewardoor.enums.SocialType
import com.rewardoor.enums.WiseScoreLevel
import com.rewardoor.model.SBTReward
import com.rewardoor.model.SbtSetting
import com.rewardoor.model.UserDcTgShareLink
import com.rewardoor.model.UserWiseScore
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("wiseScore")
class WiseScoreController(
    val wiseScoreService: WiseScoreService,
    val wiseInviteService: WiseInviteService,
    val telegramService: TelegramService,
    val sbtRewardRepo: SBTRewardRepository,
    private val projectService: ProjectService,
    private val tonSocietySyncRepository: TonSocietySyncRepository
) {
    @GetMapping("/score")
    fun getWiseScore(): Any {
        val principal = SecurityContextHolder.getContext().authentication as AddressAuthentication
        val userId = principal.userId
        val current = wiseScoreService.getScoreById(userId)
            ?: return mapOf(
                "code" to 400,
                "message" to "user has not yet generated WiseScore."
            )

        val userWiseScore = wiseScoreService.addScoreResult(userId)
        val shareLinks = wiseScoreService.getUserShareLinks(userId)
        for (link in shareLinks) {
            if (link.socialType == SocialType.DISCORD.code) {
                wiseScoreService.appendDiscordInfo(userId, link)
            } else if (link.socialType == SocialType.TELEGRAM.code) {
                wiseScoreService.appendTelegramInfo(link)
            }
        }
        userWiseScore.userDcTgShareLink = shareLinks
        return mapOf(
            "code" to 200,
            "message" to "user has already generated  WiseScore",
            "userWiseScore" to userWiseScore
        )
    }

    // generate wiseScore without invite logic check
    @GetMapping("/getScore")
    fun getScore(): Any {
        val principal = SecurityContextHolder.getContext().authentication as AddressAuthentication
        val userId = principal.userId
        val userWiseScore = wiseScoreService.addScoreResult(userId)
        val shareLinks = wiseScoreService.getUserShareLinks(userId)
        for (link in shareLinks) {
            if (link.socialType == SocialType.DISCORD.code) {
                wiseScoreService.appendDiscordInfo(userId, link)
            } else if (link.socialType == SocialType.TELEGRAM.code) {
                wiseScoreService.appendTelegramInfo(link)
            }
        }
        userWiseScore.userDcTgShareLink = shareLinks
        return mapOf(
            "code" to 200,
            "message" to "user has already generated  WiseScore",
            "userWiseScore" to userWiseScore
        )
    }

    @GetMapping("/{userId}")
    fun getWiseScore(@PathVariable("userId") userId: Long): Any {
        val current = wiseScoreService.getScoreById(userId)
            ?: return mapOf(
                "code" to 400,
                "message" to "user has not yet generated WiseScore."
            )

        val userWiseScore = wiseScoreService.addScoreResult(userId)
        val shareLinks = wiseScoreService.getUserShareLinks(userId)
        for (link in shareLinks) {
            if (link.socialType == SocialType.DISCORD.code) {
                wiseScoreService.appendDiscordInfo(userId, link)
            } else if (link.socialType == SocialType.TELEGRAM.code) {
                wiseScoreService.appendTelegramInfo(link)
            }
        }
        userWiseScore.userDcTgShareLink = shareLinks
        return mapOf(
            "code" to 200,
            "message" to "user has already generated  WiseScore",
            "userWiseScore" to userWiseScore
        )
    }

    @GetMapping("/check/{userId}")
    fun checkWiseScore(@PathVariable("userId") userId: Long): Any? {
        val userWiseScore = wiseScoreService.getScoreById(userId)
        if (userWiseScore != null) {
            val inviteTotalTimes = wiseInviteService.getInviteCode(userId, 1)?.totalTimes ?: 0
            val wiseTotalScore = userWiseScore.totalScore
            val wiseScoreLevel = WiseScoreLevel.values().first { wiseTotalScore < it.totalScore }
            val levelInviteTotalTimes = wiseScoreLevel.inviteTotalTimes
            if (levelInviteTotalTimes > inviteTotalTimes) {
                return mapOf(
                    "code" to 200,
                    "message" to "user has already generated  WiseScore",
                    "userWiseScore" to userWiseScore,
                    "shouldIncreaseInviteCount" to true,
                    "newInviteCount" to levelInviteTotalTimes
                )
            }
            return mapOf(
                "code" to 200,
                "message" to "user has already generated  WiseScore",
                "userWiseScore" to userWiseScore,
                "shouldIncreaseInviteCount" to false,
                "newInviteCount" to 0
            )
        } else {
            return mapOf(
                "code" to 400,
                "message" to "user has not yet generated WiseScore."
            )
        }

    }

    @GetMapping("/mint")
    fun mintSBTUrl(): Any? {
        val principal = SecurityContextHolder.getContext().authentication as AddressAuthentication
        val userId = principal.userId
        if (wiseScoreService.getScoreById(userId) == null) {
            return mapOf(
                "code" to 400,
                "status" to "error",
                "message" to "the user wiseScore has not created yet"
            )
        }
        val userLink = wiseScoreService.getUserSBTLink(userId, 371, -1L, -1L)
        if (userLink["status"] == "error") {
            if (userLink["link"] == "reward link with such activity id and wallet address already created") {
                print("user $userId for activity 371 is already created")
            }
            return mapOf(
                "code" to 400,
                "status" to "error",
                "message" to userLink["link"]
            )
        } else if (userLink["status"] == "hasMinted") {
            return mapOf(
                "code" to 4004,
                "status" to "hasMinted",
                "message" to "the user has minted sbt",
                "link" to userLink["link"]
            )
        } else if (userLink["link"] == "") {
            return mapOf(
                "code" to 40004,
                "status" to "error",
                "message" to "Get mint url failed"
            )
        } else {
            return mapOf(
                "code" to 200,
                "status" to "minting",
                "message" to "Get mint url success",
                "link" to userLink["link"]
            )
        }
    }

    @GetMapping("/leaderBoard")
    fun getLeaderBoard(): List<UserWiseScore> {
//        val scores = wiseScoreService.getTop100Score()
        val scores = wiseScoreService.getTop500ScoreFromRankRedis()
        scores.forEach {
            it.avatar =
                "https://api.dicebear.com/7.x/fun-emoji/svg?seed=${it.userId}&radius=50&backgroundColor=059ff2,fcbc34,d84be5,f6d594,ffd5dc,ffdfbf,d1d4f9,c0aede,b6e3f4&backgroundType=gradientLinear&backgroundRotation=30,60&eyes[]&mouth[]"
            if (it.addressType == 2) {
                val str = it.address ?: ""
                it.address = replaceMiddleWithStar(str)
            }
        }
        return scores
    }

    @GetMapping("/leaderBoard/socialScore")
    fun getSocialLeaderBoard(): List<UserWiseScore> {
        val scores = wiseScoreService.getTop100SocialScore()
//        val scores = wiseScoreService.getTop1000SocialScoreFromRankRedis()
        scores.forEach {
            it.avatar =
                "https://api.dicebear.com/7.x/fun-emoji/svg?seed=${it.userId}&radius=50&backgroundColor=059ff2,fcbc34,d84be5,f6d594,ffd5dc,ffdfbf,d1d4f9,c0aede,b6e3f4&backgroundType=gradientLinear&backgroundRotation=30,60&eyes[]&mouth[]"
            if (it.addressType == 2) {
                val str = it.address ?: ""
                it.address = replaceMiddleWithStar(str)
            }
        }
        return scores
    }

    @GetMapping("/leaderBoard/engagementScore")
    fun getEngageLeaderBoard(): List<UserWiseScore> {
        val scores = wiseScoreService.getTop100EngageScore()
//        val scores = wiseScoreService.getTop1000EngageScoreFromRankRedis()
        scores.forEach {
            it.avatar =
                "https://api.dicebear.com/7.x/fun-emoji/svg?seed=${it.userId}&radius=50&backgroundColor=059ff2,fcbc34,d84be5,f6d594,ffd5dc,ffdfbf,d1d4f9,c0aede,b6e3f4&backgroundType=gradientLinear&backgroundRotation=30,60&eyes[]&mouth[]"
            if (it.addressType == 2) {
                val str = it.address ?: ""
                it.address = replaceMiddleWithStar(str)
            }
        }
        return scores
    }

//    @GetMapping("initSBTLink")
//    fun initSBTLink(): Map<Long, String> {
//        return wiseScoreService.updateSBTLink()
//    }

    @GetMapping("/top3")
    fun getTop3WiseScore(): List<UserWiseScore> {
        val top3Scores = wiseScoreService.getTop100Score().take(3)
        top3Scores.forEach {
            it.avatar =
                "https://api.dicebear.com/7.x/fun-emoji/svg?seed=${it.userId}&radius=50&backgroundColor=059ff2,fcbc34,d84be5,f6d594,ffd5dc,ffdfbf,d1d4f9,c0aede,b6e3f4&backgroundType=gradientLinear&backgroundRotation=30,60&eyes[]&mouth[]"
            if (it.addressType == 2) {
                val str = it.address ?: ""
                it.address = replaceMiddleWithStar(str)
            }
        }
        return top3Scores
    }

    @GetMapping("/topWealth")
    fun getTopWealth(): List<UserWiseScore> {
        return wiseScoreService.getTop500WealthWiseScore()
    }

    @GetMapping("/tonWiseScore")
    fun addTonAndEvmWiseScore() {
        return wiseScoreService.addAllTmpToScore()
    }

    private fun replaceMiddleWithStar(input: String): String {
        return if (input.length <= 2) {
            input
        } else {
            val firstCharacter = input.first()
            val lastCharacter = input.last()
            val middle = "*".repeat(input.length - 2)
            firstCharacter + middle + lastCharacter
        }
    }

    @PostMapping("/addLink")
    fun submitLink(@RequestBody dcTgLinks: UserDcTgShareLink): SimpleCodeResponseEntity<List<UserDcTgShareLink>> {
        val principal = SecurityContextHolder.getContext().authentication as AddressAuthentication
        val hash = TelegramService.extractPrivateLink(dcTgLinks.shareLink)
        if (hash != null) {
            return SimpleCodeResponseEntity.failed(
                410, "The private channel and group can't be attested so far.\n" +
                        "Maybe you could set it to public and try again."
            )
        }
        dcTgLinks.userId = principal.userId
        val (result, message) = wiseScoreService.addUserShareLink(dcTgLinks)
        return if (result != null) {
            SimpleCodeResponseEntity.success("", result)
        } else {
            SimpleCodeResponseEntity.failed(410, message)
        }
    }

    @GetMapping("/shareLinks/{userId}")
    fun getLinks(@PathVariable("userId") userId: Long): List<UserDcTgShareLink> {
        return wiseScoreService.getUserShareLinks(userId)
    }

    @GetMapping("/onboard")
    fun getOnboardInfo(): Any {
        val principal = SecurityContextHolder.getContext().authentication as AddressAuthentication
        val userId = principal.userId
        val isTgPremium = telegramService.getUserTgInfo(userId)?.isPremium ?: false
        val tgGramEliteSBT = tonSocietySyncRepository.getTonSyncHistoryByActivityId(803)
        val tBookTonPioneerSBT = tonSocietySyncRepository.getTonSyncHistoryByActivityId(1467)
        val xConnectSBT = tonSocietySyncRepository.getTonSyncHistoryByActivityId(1626)
        return mapOf(
            "code" to 200,
            "isTgPremium" to isTgPremium,
            "tgGramEliteSBT" to tgGramEliteSBT,
            "tBookTonPioneerSBT" to tBookTonPioneerSBT,
            "xConnectSBT" to xConnectSBT
        )
    }

    @GetMapping("/sbtGallery")
    fun getSbtGallery(): SbtGalleryInfo {
        val principal = SecurityContextHolder.getContext().authentication as AddressAuthentication
        val userId = principal.userId
        val allSBTs = sbtRewardRepo.getAllSBTs()
        val ogSBTs = allSBTs.filter { it.category == 1 }
            .groupBy { it.activityId }
            .mapValues { entry ->
                // 每个 activityId 中取 sbtId 最大的一条，也就是最新生成的sbt
                entry.value.maxByOrNull { it.sbtId }
            }
            .values
            .filterNotNull()
        val ogSbtSetting = mutableListOf<SbtSetting>()
        val priority1 = mutableListOf<SbtSetting>()
        val priority2 = mutableListOf<SbtSetting>()
        val priority3 = mutableListOf<SbtSetting>()
        val remaining = mutableListOf<SbtSetting>()
        for (ogSBT in ogSBTs) {
            val sbtSetting = projectService.getSbtSettingById(ogSBT.sbtId, userId)
            if (sbtSetting != null) {
                when (sbtSetting.sbtActivityId) {
                    in 1469..1474 -> priority1.add(sbtSetting)
                    803 -> priority2.add(sbtSetting)
                    1467 -> priority3.add(sbtSetting)
                    1626 -> priority3.add(sbtSetting)
                    else -> remaining.add(sbtSetting)
                }
            }
        }
        ogSbtSetting.addAll(priority1) // 添加第一优先级的设置
        ogSbtSetting.addAll(priority2) // 添加第二优先级的设置
        ogSbtSetting.addAll(priority3) // 添加第三优先级的设置
        ogSbtSetting.addAll(remaining) // 最后添加剩余的设置

        val defiSbtSetting = getSbtSettingByCategory(allSBTs, 2, userId)
        val memeSbtSetting = getSbtSettingByCategory(allSBTs, 3, userId)
        val gameSbtSetting = getSbtSettingByCategory(allSBTs, 4, userId)
        val premiumSbtSetting = getSbtSettingByCategory(allSBTs, 5, userId)
        val tonPioneerSbtSetting = getSbtSettingByCategory(allSBTs, 6, userId)
        val sbtGalleryInfo = SbtGalleryInfo(
            ogSBTs = ogSbtSetting,
            defiSBTs = defiSbtSetting,
            memeSBTs = memeSbtSetting,
            gameSBTs = gameSbtSetting,
            premiumSBTs = premiumSbtSetting,
            tonPioneerSBTs = tonPioneerSbtSetting
        )
        return sbtGalleryInfo
    }

    private fun getSbtSettingByCategory(sbts: List<SBTReward>, category: Int, userId: Long): List<SbtSetting> {
        val sbtSetting = mutableListOf<SbtSetting>()
        val categorySBTs = sbts.filter { it.category == category }
            .groupBy { it.activityId }
            .mapValues { entry ->
                // 每个 activityId 中取 sbtId 最大的一条，也就是最新生成的sbt
                entry.value.maxByOrNull { it.sbtId }
            }
            .values
            .filterNotNull()
            .sortedByDescending { it.activityId }
        for (sbt in categorySBTs) {
            val setting = projectService.getSbtSettingById(sbt.sbtId, userId)
            if (setting != null) {
                sbtSetting.add(setting)
            }
        }
        return sbtSetting
    }

    class SbtGalleryInfo(
        val ogSBTs: List<SbtSetting> = emptyList(),
        val defiSBTs: List<SbtSetting> = emptyList(),
        val memeSBTs: List<SbtSetting> = emptyList(),
        val gameSBTs: List<SbtSetting> = emptyList(),
        val premiumSBTs: List<SbtSetting> = emptyList(),
        val tonPioneerSBTs: List<SbtSetting> = emptyList()
    )

}